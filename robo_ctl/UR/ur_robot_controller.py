"""
UR Robot Controller Module

This module provides a high-level interface for controlling UR robots
with Robotiq grippers using RTDE communication.
"""

import rtde_control
import rtde_receive
from .robotiq_gripper_control import RobotiqGripper
import logging
from typing import List, Optional


class URRobotController:
    """
    A comprehensive controller class for UR robots with Robotiq gripper.
    
    This class encapsulates RTDE control and receive interfaces, along with
    gripper control functionality to provide a clean API for robot operations.
    
    Attributes:
        robot_host (str): IP address of the robot
        rtde_control (rtde_control.RTDEControlInterface): RTDE control interface
        rtde_receive (rtde_receive.RTDEReceiveInterface): RTDE receive interface
        gripper (RobotiqGripper): Robotiq gripper controller
    """
    
    def __init__(self, robot_host: str):
        """
        Initialize UR robot controller.
        
        Args:
            robot_host (str): IP address of the robot (e.g., "*************")
            
        Raises:
            ConnectionError: If unable to connect to the robot
        """
        self.robot_host = robot_host
        self.rtde_control = None
        self.rtde_receive = None
        self.gripper = None
        self._connected = False
        
        try:
            # Initialize RTDE interfaces
            self.rtde_control = rtde_control.RTDEControlInterface(robot_host)
            self.rtde_receive = rtde_receive.RTDEReceiveInterface(robot_host)
            
            # Initialize gripper
            self.gripper = RobotiqGripper(self.rtde_control)
            
            self._connected = True
            logging.info(f"Successfully connected to robot at {robot_host}")
            
        except Exception as e:
            logging.error(f"Failed to connect to robot at {robot_host}: {e}")
            raise ConnectionError(f"Unable to connect to robot: {e}")
    
    def activate_gripper(self) -> bool:
        """
        Activate the Robotiq gripper.
        
        Returns:
            bool: True if activation succeeded, False otherwise
            
        Raises:
            RuntimeError: If gripper is not initialized
        """
        if not self.gripper:
            raise RuntimeError("Gripper not initialized")
            
        try:
            result = self.gripper.activate()
            if result:
                logging.info("Gripper activated successfully")
            else:
                logging.warning("Gripper activation failed")
            return result
        except Exception as e:
            logging.error(f"Error activating gripper: {e}")
            return False
    
    def open_gripper(self) -> bool:
        """
        Open the gripper.
        
        Returns:
            bool: True if operation succeeded, False otherwise
        """
        if not self.gripper:
            raise RuntimeError("Gripper not initialized")
            
        try:
            result = self.gripper.open()
            if result:
                logging.info("Gripper opened successfully")
            else:
                logging.warning("Gripper open operation failed")
            return result
        except Exception as e:
            logging.error(f"Error opening gripper: {e}")
            return False
    
    def close_gripper(self) -> bool:
        """
        Close the gripper.
        
        Returns:
            bool: True if operation succeeded, False otherwise
        """
        if not self.gripper:
            raise RuntimeError("Gripper not initialized")
            
        try:
            result = self.gripper.close()
            if result:
                logging.info("Gripper closed successfully")
            else:
                logging.warning("Gripper close operation failed")
            return result
        except Exception as e:
            logging.error(f"Error closing gripper: {e}")
            return False
    
    def set_gripper_speed(self, speed: int) -> bool:
        """
        Set gripper speed.
        
        Args:
            speed (int): Speed as percentage [0-100]
            
        Returns:
            bool: True if operation succeeded, False otherwise
        """
        if not self.gripper:
            raise RuntimeError("Gripper not initialized")
            
        if not 0 <= speed <= 100:
            raise ValueError("Speed must be between 0 and 100")
            
        return self.gripper.set_speed(speed)
    
    def set_gripper_force(self, force: int) -> bool:
        """
        Set gripper force.
        
        Args:
            force (int): Force as percentage [0-100]
            
        Returns:
            bool: True if operation succeeded, False otherwise
        """
        if not self.gripper:
            raise RuntimeError("Gripper not initialized")
            
        if not 0 <= force <= 100:
            raise ValueError("Force must be between 0 and 100")
            
        return self.gripper.set_force(force)
    
    def move_linear(self, pose: List[float], velocity: float, acceleration: float) -> bool:
        """
        Move robot to target pose using linear motion.
        
        Args:
            pose (List[float]): Target pose [x, y, z, rx*θ, ry*θ, rz*θ] in axis-angle representation
            velocity (float): Tool speed in m/s
            acceleration (float): Tool acceleration in m/s²
            
        Returns:
            bool: True if movement succeeded, False otherwise
            
        Raises:
            ValueError: If pose format is invalid
        """
        if not self.rtde_control:
            raise RuntimeError("RTDE control not initialized")
            
        if len(pose) != 6:
            raise ValueError("Pose must contain 6 elements [x, y, z, rx*θ, ry*θ, rz*θ]")
            
        if velocity <= 0 or acceleration <= 0:
            raise ValueError("Velocity and acceleration must be positive")
            
        try:
            result = self.rtde_control.moveL(pose, velocity, acceleration)
            if result:
                logging.info(f"Linear movement to {pose} completed successfully")
            else:
                logging.warning(f"Linear movement to {pose} failed")
            return result
        except Exception as e:
            logging.error(f"Error during linear movement: {e}")
            return False
    
    def get_tcp_pose(self) -> Optional[List[float]]:
        """
        Get current TCP (Tool Center Point) pose.
        
        Returns:
            List[float]: Current TCP pose [x, y, z, rx*θ, ry*θ, rz*θ] or None if error
        """
        if not self.rtde_receive:
            raise RuntimeError("RTDE receive not initialized")
            
        try:
            pose = self.rtde_receive.getActualTCPPose()
            return pose
        except Exception as e:
            logging.error(f"Error getting TCP pose: {e}")
            return None
    
    def get_joint_positions(self) -> Optional[List[float]]:
        """
        Get current joint positions.
        
        Returns:
            List[float]: Current joint positions in radians or None if error
        """
        if not self.rtde_receive:
            raise RuntimeError("RTDE receive not initialized")
            
        try:
            joints = self.rtde_receive.getActualQ()
            return joints
        except Exception as e:
            logging.error(f"Error getting joint positions: {e}")
            return None
    
    def move_joints(self, joint_positions: List[float], velocity: float, acceleration: float) -> bool:
        """
        Move robot to target joint positions.
        
        Args:
            joint_positions (List[float]): Target joint positions in radians
            velocity (float): Joint speed in rad/s
            acceleration (float): Joint acceleration in rad/s²
            
        Returns:
            bool: True if movement succeeded, False otherwise
        """
        if not self.rtde_control:
            raise RuntimeError("RTDE control not initialized")
            
        if len(joint_positions) != 6:
            raise ValueError("Joint positions must contain 6 elements")
            
        try:
            result = self.rtde_control.moveJ(joint_positions, velocity, acceleration)
            if result:
                logging.info("Joint movement completed successfully")
            else:
                logging.warning("Joint movement failed")
            return result
        except Exception as e:
            logging.error(f"Error during joint movement: {e}")
            return False
    
    def stop_robot(self) -> bool:
        """
        Stop robot movement immediately.
        
        Returns:
            bool: True if stop succeeded, False otherwise
        """
        if not self.rtde_control:
            raise RuntimeError("RTDE control not initialized")
            
        try:
            result = self.rtde_control.stopL()
            logging.info("Robot stopped")
            return result
        except Exception as e:
            logging.error(f"Error stopping robot: {e}")
            return False
    
    def is_connected(self) -> bool:
        """
        Check if robot is connected.
        
        Returns:
            bool: True if connected, False otherwise
        """
        return self._connected
    
    def disconnect(self):
        """
        Disconnect from the robot and clean up resources.
        """
        try:
            if self.rtde_control:
                self.rtde_control.stopScript() 
                self.rtde_control.disconnect()
            if self.rtde_receive:
                self.rtde_receive.disconnect()
            self._connected = False
            logging.info(f"Disconnected from robot at {self.robot_host}")
        except Exception as e:
            logging.error(f"Error during disconnect: {e}")
    
    def __enter__(self):
        """Context manager entry."""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit - automatically disconnect."""
        self.disconnect()
    
    def __del__(self):
        """Destructor - ensure clean disconnection."""
        if hasattr(self, '_connected') and self._connected:
            self.disconnect() 