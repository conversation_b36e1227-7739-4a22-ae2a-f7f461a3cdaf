#! /usr/bin/env python
# coding=utf-8
"""
AuboController

A lightweight wrapper for the Aubo Python SDK (pyaubo_sdk), providing more user-friendly
linear/circular motion, speed management, and position query interfaces for secondary development.

Features:
- Connect/login to RPC
- Speed configuration caching (tool/joint speed and acceleration) and setting interfaces
- Linear motion moveLine (supports moving from current pose to target, or from start point to end point)
- Circular motion moveCircle (via intermediate point to end point)
- Get current joint angles and TCP pose
- Optional blocking wait for motion completion (based on execId polling)

Notes:
- The SDK does not provide a "global speed setting" interface. Speed/acceleration are typically
  passed as motion command parameters. This class internally caches "default speed parameters"
  and uses them or overrides them with method parameters when issuing motion commands.
- Please follow the example for speed units. The example converts angle units to radians uniformly,
  such as 250*(pi/180).
- Before use, please ensure the robotic arm is powered on, unlocked, and in a movable state
  (refer to example_startup.py).
"""
from __future__ import annotations

import time
import math
from typing import List, Optional, Tuple

import pyaubo_sdk


M_PI = 3.14159265358979323846


class AuboController:
    def __init__(self, ip: str = "127.0.0.1", port: int = 30004,
                 username: str = "aubo", password: str = "123456") -> None:
        self.ip = ip
        self.port = port
        self.username = username
        self.password = password

        self._client = pyaubo_sdk.RpcClient()
        self.robot_name: Optional[str] = None

        # Speed parameter cache (defaults read from RobotConfig, can be overridden by set_*)
        self.tool_speed: Optional[float] = None   # TCP speed (example uses radians)
        self.tool_acc: Optional[float] = None     # TCP acceleration
        self.joint_speed: Optional[float] = None  # Joint speed (rad/s)
        self.joint_acc: Optional[float] = None    # Joint acceleration (rad/s^2)

    # ---------- Connection/Login ----------
    def connect(self, login: bool = True) -> bool:
        """Connect to RPC (with optional auto-login). Returns True on success."""
        self._client.connect(self.ip, self.port)
        if not self._client.hasConnected():
            return False
        if login:
            return self.login(self.username, self.password)
        return True

    def login(self, username: str, password: str) -> bool:
        self._client.login(username, password)
        if not self._client.hasLogined():
            return False
        # Select the first robot
        names = self._client.getRobotNames()
        if not names:
            return False
        self.robot_name = names[0]
        # Initialize speed cache
        self._init_default_speeds()
        return True

    def _assert_ready(self) -> None:
        if not self._client.hasConnected():
            raise RuntimeError("RPC not connected")
        if not self._client.hasLogined():
            raise RuntimeError("RPC not logged in")
        if not self.robot_name:
            raise RuntimeError("Robot name not obtained")

    # ---------- Speed Related ----------
    def _init_default_speeds(self) -> None:
        """Read default speed parameters from RobotConfig as initial speeds for this class."""
        try:
            iface = self._client.getRobotInterface(self.robot_name)
            cfg = iface.getRobotConfig()
            # Note: In the example, linear speed/acceleration and joint speed/acceleration units are passed as rad/s, rad/s^2
            self.tool_speed = cfg.getDefaultToolSpeed()
            self.tool_acc = cfg.getDefaultToolAcc()
            self.joint_speed = cfg.getDefaultJointSpeed()
            self.joint_acc = cfg.getDefaultJointAcc()
        except Exception:
            # Keep as None on error, need to provide or fall back to example defaults when calling motion interfaces
            pass

    def set_tool_speed_acc(self, speed: float, acc: float) -> None:
        self.tool_speed = float(speed)
        self.tool_acc = float(acc)

    def set_joint_speed_acc(self, speed: float, acc: float) -> None:
        self.joint_speed = float(speed)
        self.joint_acc = float(acc)

    # ---------- Status Retrieval ----------
    def get_joint_positions(self) -> List[float]:
        self._assert_ready()
        return self._client.getRobotInterface(self.robot_name).getRobotState().getJointPositions()

    def get_tcp_pose(self) -> List[float]:
        self._assert_ready()
        return self._client.getRobotInterface(self.robot_name).getRobotState().getTcpPose()

    # ---------- Runtime Control ----------
    def start_runtime(self) -> None:
        """Start runtime (example calls this before sending motion commands)."""
        self._client.getRuntimeMachine().start()

    def stop_runtime(self) -> None:
        self._client.getRuntimeMachine().stop()

    # ---------- Synchronous Wait ----------
    def wait_for_motion(self) -> int:
        """Block wait for current motion to complete, based on execId polling. Returns -1 on failure, no return value on success."""
        self._assert_ready()
        iface = self._client.getRobotInterface(self.robot_name)
        mc = iface.getMotionControl()

        # Wait for execId to be generated
        cnt = 0
        while mc.getExecId() == -1:
            cnt += 1
            if cnt > 5:
                return -1
            time.sleep(0.05)
            print("getExecId: ", mc.getExecId())
        # Wait for execId to change (motion end)
        cur_id = mc.getExecId()
        while True:
            if mc.getExecId() != cur_id:
                break
            time.sleep(0.05)

    # ---------- Motion Commands ----------
    def moveJoint(self, q: List[float],
                   speed: Optional[float] = None,
                   acc: Optional[float] = None,
                   blend_radius: float = 0.0,
                   ref: int = 0,
                   wait: bool = True) -> int:
        """Joint motion wrapper.
        Parameter meanings follow the example: speed/acc are passed in rad/s, rad/s^2; 
        blend and reference frame use default values.
        Returns SDK return code, 0 for success.
        """
        self._assert_ready()
        spd = float(speed if speed is not None else (self.joint_speed if self.joint_speed is not None else 180 * (M_PI / 180)))
        ac = float(acc if acc is not None else (self.joint_acc if self.joint_acc is not None else 1000000 * (M_PI / 180)))

        self.start_runtime()
        ret = self._client.getRobotInterface(self.robot_name).getMotionControl() \
            .moveJoint(q, spd, ac, blend_radius, ref)
        if wait:
            wait_result = self.wait_for_motion()
            # End runtime (keep consistent with example)
            self.stop_runtime()
            if wait_result == -1:
                # Wait failed
                return -1 if ret == 0 else ret
        else:
            # Non-blocking also ends runtime, managed by caller, no automatic stop here
            self.stop_runtime()
        return ret

    def moveLine(self,
                  target_pose: List[float],
                  start_pose: Optional[List[float]] = None,
                  speed: Optional[float] = None,
                  acc: Optional[float] = None,
                  blend_radius: float = 0.0,
                  ref: int = 0,
                  wait: bool = True) -> int:
        """Linear motion:
        - If start_pose is provided, first move linearly to start_pose, then to target_pose;
        - Otherwise, move linearly from current position to target_pose.
        Returns SDK return code, 0 for success.
        """
        self._assert_ready()
        spd = float(speed if speed is not None else (self.tool_speed if self.tool_speed is not None else 250 * (M_PI / 180)))
        ac = float(acc if acc is not None else (self.tool_acc if self.tool_acc is not None else 1000 * (M_PI / 180)))

        self.start_runtime()
        mc = self._client.getRobotInterface(self.robot_name).getMotionControl()
        ret = 0
        try:
            if start_pose is not None:
                r1 = mc.moveLine(start_pose, spd, ac, blend_radius, ref)
                if r1 != 0:
                    ret = r1
                    return ret
                if wait and self.wait_for_motion() == -1:
                    return -1
            ret = mc.moveLine(target_pose, spd, ac, blend_radius, ref)
            if wait:
                wait_result = self.wait_for_motion()
                if wait_result == -1:
                    return -1 if ret == 0 else ret
            return ret
        finally:
            self.stop_runtime()

    def moveCircle(self,
                    via_pose: List[float],
                    target_pose: List[float],
                    speed: Optional[float] = None,
                    acc: Optional[float] = None,
                    blend_radius: float = 0.0,
                    ref: int = 0) -> int:
        """Circular motion: from current pose, through via_pose to target_pose.
        Returns SDK return code, 0 for success.
        """
        self._assert_ready()
        spd = float(speed if speed is not None else (self.tool_speed if self.tool_speed is not None else 180 * (M_PI / 180)))
        ac = float(acc if acc is not None else (self.tool_acc if self.tool_acc is not None else 1000000 * (M_PI / 180)))

        self.start_runtime()
        ret = self._client.getRobotInterface(self.robot_name).getMotionControl() \
            .moveCircle(via_pose, target_pose, spd, ac, blend_radius, ref)
        
        self.stop_runtime()
        return ret

# ---------------------- Usage Example ----------------------
if __name__ == '__main__':
    ctrl = AuboController(ip="127.0.0.1", port=30004)
    if not ctrl.connect(login=True):
        print("Connection or login failed")
        raise SystemExit(1)
    print("Connected and logged in successfully, robot:", ctrl.robot_name)

    # Optional: Adjust speed (following the example's parameter passing style)
    ctrl.set_tool_speed_acc(250 * (M_PI / 180), 1000 * (M_PI / 180))
    ctrl.set_joint_speed_acc(180 * (M_PI / 180), 1000000 * (M_PI / 180))

    # Read current status
    try:
        print("Current joint angles:", ctrl.get_joint_positions())
        print("Current TCP pose:", ctrl.get_tcp_pose())
    except Exception as e:
        print("Failed to read status:", e)

    # Linear motion (from current position to target)
    p1 = [-0.155944, -0.727344, 0.439066, 3.05165, 0.0324355, 1.80417]
    ret = ctrl.moveLine(target_pose=p1, wait=True)
    print("moveLine ret:", ret)



