import numpy as np
import cv2
import torch
import sys
from pathlib import Path

# Add preprocess directory to Python path
sys.path.append(str(Path(__file__).resolve().parent / 'preprocess'))
from preprocess.infer_wrap import YOLOv8TRTDetector
from template_matcher.utils_rot import calculate_angle_with_horizontal
from template_matcher.utils_matcher import detect_circle_in_roi

# ----------------------------
# Global configuration (can be externalized)
# ----------------------------
# Camera intrinsics matrix K (fx, fy, cx, cy). 
K_INTRINSICS = np.array([
    [600.0,   0.0, 640.0],
    [  0.0, 600.0, 360.0],
    [  0.0,   0.0,   1.0]
], dtype=np.float32)

# Depth scale: raw_depth / depth_scale -> meters (adjust to Depth.tif scale)
DEPTH_SCALE = 1000.0

# Camera extrinsic parameters (camera to world transformation). 
CAMERA_EXTRINSIC = np.array([
    [1.0, 0.0, 0.0, 0.0],
    [0.0, 1.0, 0.0, 0.0],
    [0.0, 0.0, 1.0, 0.0],
    [0.0, 0.0, 0.0, 1.0]
], dtype=np.float32)

# Rotation bias (degrees). Keep current approach for now; adjust after on-site calibration.
ROTATION_BIAS_DEG = -(90.0 + 28.44)


def load_and_detect_circles(color_path, detector):
    """Load color image, then detect circles in ROIs.

    Returns
    -------
    (large_center, small_center, color_image)
        large_center: (u, v) pixel for large circle or None
        small_center: (u, v) pixel for small circle or None
        color_image : BGR image with visualization
    """
    # Load color image
    color_image = cv2.imread(color_path)
    if color_image is None:
        print(f"Failed to load color image from {color_path}")
        return None, None, None
    
    print(f"Loaded color image: {color_image.shape}")
    
    # Convert to grayscale and enhance contrast
    scene_gray = cv2.cvtColor(color_image, cv2.COLOR_BGR2GRAY)
    blur = cv2.GaussianBlur(scene_gray, (9,9), 2)
    clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
    enh = clahe.apply(blur)
        
    # Perform YOLO detection
    detections = detector.predict(color_image)
    
    # Initialize variables for ROI centers
    large_center = None
    small_center = None
    
    # Process detections
    for bbox, label in zip(detections['bboxes'], detections['labels']):
        # Convert bounding box coordinates to integers
        x1, y1, x2, y2 = map(int, bbox)
        # Draw ROI bounding box
        color = (0, 255, 0) if label == 1 else (255, 0, 0)  # Green for large (1), Blue for small (2)
        cv2.rectangle(color_image, (x1, y1), (x2, y2), color, 2)
        
        # Extract ROI from scene image
        roi_gray = enh[y1:y2, x1:x2]
        
        # Map labels to ROI types
        # label = 1 corresponds to large_roi
        # label = 2 corresponds to small_roi
        if label == 1:
            # Detect circles in large ROI
            large_circles = detect_circle_in_roi(roi_gray, x1, y1, "large")
            if large_circles:
                # Use the first detected circle
                large_center = (large_circles[0][0], large_circles[0][1])
                print(f"Large circle center: {large_center}, radius: {large_circles[0][2]}")
                # Draw circle and center
                cv2.circle(color_image, large_center, large_circles[0][2], (0, 0, 255), 2)  # Red circle boundary
                cv2.circle(color_image, large_center, 1, (0, 255, 255), 3)  # Yellow center point
        elif label == 2:
            # Detect circles in small ROI
            small_circles = detect_circle_in_roi(roi_gray, x1, y1, "small")
            if small_circles:
                # Use the first detected circle
                small_center = (small_circles[0][0], small_circles[0][1])
                print(f"Small circle center: {small_center}, radius: {small_circles[0][2]}")
                # Draw circle and center
                cv2.circle(color_image, small_center, small_circles[0][2], (0, 0, 255), 2)  # Red circle boundary
                cv2.circle(color_image, small_center, 1, (0, 255, 255), 3)  # Yellow center point
    
    # Check if both centers were detected
    if large_center is None or small_center is None:
        print("Could not detect both large and small circles.")
        return None, None, None
    
    # Display visualized image
    cv2.namedWindow('Detection Visualization', cv2.WINDOW_NORMAL)
    cv2.imshow('Detection Visualization', color_image)
    cv2.waitKey(0)
    cv2.destroyAllWindows()
    
    return large_center, small_center, color_image

def extract_3d_coordinates_from_depth_image(depth_path, pixel_coords, image_shape, K, depth_scale=1.0):
    """Extract 3D camera coordinates from depth image based on pixel coordinates.

    Parameters
    ----------
    depth_path : str
        Path to depth image (single-channel). Units: raw/depth_scale -> meters.
    pixel_coords : list[tuple[int, int]]
        List of (u, v) pixel coordinates.
    image_shape : tuple
        Shape of the corresponding color image (H, W, C) to validate bounds.
    K : np.ndarray
        3x3 camera intrinsics matrix.
    depth_scale : float
        Scale divisor to convert raw depth to meters.

    Returns
    -------
    np.ndarray
        Array of shape (N, 3) with 3D camera coordinates.
    """
    height, width = image_shape[:2]
    depth = cv2.imread(depth_path, cv2.IMREAD_UNCHANGED)
    if depth is None:
        print(f"Failed to load depth image from {depth_path}")
        return None
    if depth.shape[0] != height or depth.shape[1] != width:
        print(f"Depth size ({depth.shape[1]}x{depth.shape[0]}) doesn't match image size ({width}x{height})")
        return None

    fx, fy = float(K[0, 0]), float(K[1, 1])
    cx, cy = float(K[0, 2]), float(K[1, 2])

    coords_3d = []
    for u, v in pixel_coords:
        if 0 <= v < height and 0 <= u < width:
            z_raw = float(depth[v, u])
            z = z_raw / float(depth_scale)
            if z <= 0:
                print(f"Invalid depth at pixel ({u}, {v}): {z_raw}")
                return None
            x = (u - cx) * z / fx
            y = (v - cy) * z / fy
            coords_3d.append([x, y, z])
            print(f"Pixel ({u}, {v}) -> 3D coord: ({x:.3f}, {y:.3f}, {z:.3f})")
        else:
            print(f"Pixel coordinate ({u}, {v}) is out of image bounds")
            return None
    return np.array(coords_3d, dtype=np.float32)

def transform_camera_to_world(camera_coords, extrinsic_matrix):
    """Transform coordinates from camera coordinate system to world coordinate system."""
    # Convert to homogeneous coordinates
    homogeneous_coords = np.column_stack([camera_coords, np.ones(len(camera_coords))])
    
    # Apply transformation
    world_coords_homogeneous = (extrinsic_matrix @ homogeneous_coords.T).T
    
    # Convert back to Cartesian coordinates
    world_coords = world_coords_homogeneous[:, :3]
    
    return world_coords

def calculate_rotation(color_path, depth_path, detector):
    """
    Calculate rotation angle based on circle positions in 3D space.
    
    Args:
        color_path: Path to color image
        pcd_path: Path to point cloud file
        detector: YOLO detector instance
    
    Returns:
        float: Rotation angle in degrees
    """
    print("=" * 50)
    print("STEP 1: ROTATION CALCULATION")
    print("=" * 50)
    
    # Load images and detect circles
    large_center, small_center, color_image = load_and_detect_circles(color_path, detector)
    if large_center is None:
        print("Failed to detect large circles")
        return None
    
    # Extract 3D coordinates from depth image
    circle_centers_3d = extract_3d_coordinates_from_depth_image(
        depth_path,
        [large_center],
        color_image.shape,
        K_INTRINSICS,
        DEPTH_SCALE
    )
    if circle_centers_3d is None:
        print("Failed to extract 3D coordinates")
        return None
    
    large_center_3d = circle_centers_3d[0]
    
    # Transform camera coordinates to world coordinates
    large_center_3d_world = transform_camera_to_world(np.array([large_center_3d]), CAMERA_EXTRINSIC)[0]
    
    # Calculate rotation angle
    # Reference TCP position
    tcp_xyz = np.array([0.76383, -0.24167, 0.36874])
    
    # Calculate initial angle in 3D space
    # Project onto XY plane for angle calculation
    large_center_xy = np.array([tcp_xyz[0], large_center_3d_world[1], large_center_3d_world[2]])
    initial_angle = calculate_angle_with_horizontal(tcp_xyz, large_center_xy)
    
    # Calculate rotation angle (same as 2D version)
    rot_angle = ROTATION_BIAS_DEG - initial_angle
    
    print(f"Large center 3D world coordinates: {large_center_3d_world}")
    print(f"Initial angle: {initial_angle:.4f} degrees")
    print(f"Rotation angle: {rot_angle:.2f} degrees")
    print("=" * 50)
    
    return rot_angle

def calculate_translation(color_path, depth_path, detector):
    """
    Calculate translation vector based on circle positions in 3D space.
    
    Args:
        color_path: Path to color image
        pcd_path: Path to point cloud file
        detector: YOLO detector instance
    
    Returns:
        tuple: (displacement_3d, large_center_3d_world)
            - displacement_3d: 3D displacement vector
            - large_center_3d_world: Large circle center in world coordinates
    """
    print("=" * 50)
    print("STEP 2: TRANSLATION CALCULATION")
    print("=" * 50)
    
    # Load images and detect circles
    large_center, small_center, color_image = load_and_detect_circles(color_path, detector)
    if large_center is None or small_center is None:
        print("Failed to detect circles")
        return None, None
    
    # Extract 3D coordinates from depth image
    circle_centers_3d = extract_3d_coordinates_from_depth_image(
        depth_path,
        [large_center, small_center],
        color_image.shape,
        K_INTRINSICS,
        DEPTH_SCALE
    )
    if circle_centers_3d is None:
        print("Failed to extract 3D coordinates")
        return None, None
    
    large_center_3d = circle_centers_3d[0]
    small_center_3d = circle_centers_3d[1]
    
    # Transform camera coordinates to world coordinates
    large_center_3d_world = transform_camera_to_world(np.array([large_center_3d]), CAMERA_EXTRINSIC)[0]
    small_center_3d_world = transform_camera_to_world(np.array([small_center_3d]), CAMERA_EXTRINSIC)[0]
    
    # Calculate displacement vector
    displacement_3d = small_center_3d_world - large_center_3d_world
    
    print(f"Large circle 3D coordinates: {large_center_3d_world}")
    print(f"Small circle 3D coordinates: {small_center_3d_world}")
    print(f"3D displacement vector: {displacement_3d}")
    print("=" * 50)
    
    return displacement_3d, large_center_3d_world

def main():
    print("=" * 60)
    print("3D OFFLINE CIRCLE DETECTION - MODULAR IMPLEMENTATION")
    print("=" * 60)
    
    # Paths for current capture (rotation or translation step)
    # Adjust to your dataset or runtime capture output
    color_path = 'data/0707_T0/Image.png'
    depth_path = 'data/0707_T0/Depth.tif'

    # YOLO detector configuration
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    yolo_engine = 'preprocess/weights/yolov8s_snap_v1.pt'
    detector = YOLOv8TRTDetector(yolo_engine, device)

    use_rot = True
    # Rotation calculation only
    if use_rot:
        print("\n" + "="*50)
        print("EXECUTING ROTATION CALCULATION")
        print("="*50)
    
        rot_angle = calculate_rotation(color_path, depth_path, detector)
        if rot_angle is not None:
            print(f"\nROTATION RESULT: {rot_angle:.2f} degrees")
    else:
        # Translation calculation only
        print("\n" + "="*50)
        print("EXECUTING TRANSLATION CALCULATION")
        print("="*50)
        
        displacement_3d, large_center_3d_world = calculate_translation(color_path, depth_path, detector)
        if displacement_3d is not None:
            print(f"\nTRANSLATION RESULT: {displacement_3d}")
    
    print("\nProgram completed. Goodbye!")


def legacy_realtime_main():
    """
    Legacy main function for backward compatibility.
    This function maintains the original coupled behavior.
    """
    print("=" * 50)
    print("LEGACY MODE: COUPLED ROTATION AND TRANSLATION")
    print("=" * 50)
    
    # Path to files (legacy sample)
    color_path = 'data/0707_T0/Image.png'
    depth_path = 'data/0707_T0/Depth.tif'

    # YOLO detector configuration
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    yolo_engine = 'preprocess/weights/yolov8s_snap_v1.pt'
    
    try:
        # Initialize YOLO detector
        detector = YOLOv8TRTDetector(yolo_engine, device)
    except:
        print("YOLO detector not available, using mock detector for testing")
        # Create a mock detector for testing
        class MockDetector:
            def predict(self, image):
                # Mock detection results - return predefined ROIs
                h, w = image.shape[:2]
                # Mock large ROI (label 1) and small ROI (label 2)
                return {
                    'bboxes': [
                        [w*0.3, h*0.3, w*0.7, h*0.7],  # Large ROI
                        [w*0.1, h*0.1, w*0.4, h*0.4]   # Small ROI
                    ],
                    'labels': [1, 2]
                }
        detector = MockDetector()
    
    print("Starting 3D circle detection and coordinate transformation")
    
    # Load images and detect circles
    large_center, small_center, color_image = load_and_detect_circles(color_path, detector)
    if large_center is None or small_center is None:
        print("Failed to detect circles")
        return
    
    # Extract 3D coordinates from depth image
    circle_centers_3d = extract_3d_coordinates_from_depth_image(
        depth_path,
        [large_center, small_center],
        color_image.shape,
        K_INTRINSICS,
        DEPTH_SCALE
    )
    if circle_centers_3d is None:
        print("Failed to extract 3D coordinates")
        return
    
    large_center_3d = circle_centers_3d[0]
    small_center_3d = circle_centers_3d[1]
    
    # Transform camera coordinates to world coordinates
    large_center_3d_world = transform_camera_to_world(np.array([large_center_3d]), CAMERA_EXTRINSIC)[0]
    small_center_3d_world = transform_camera_to_world(np.array([small_center_3d]), CAMERA_EXTRINSIC)[0]
    
    # Calculate 3D displacement
    displacement_3d = small_center_3d_world - large_center_3d_world
    
    # Calculate rotation angle
    tcp_xyz = np.array([0.76383, -0.24167, 0.36874])
    large_center_xy = np.array([tcp_xyz[0], large_center_3d_world[1], large_center_3d_world[2]])
    initial_angle = calculate_angle_with_horizontal(tcp_xyz, large_center_xy)
    rot_angle = ROTATION_BIAS_DEG - initial_angle
    
    print(f"Large circle 3D world coordinates: {large_center_3d_world}")
    print(f"Initial angle: {initial_angle:.4f} degrees")
    print(f"Rotation angle: {rot_angle:.2f} degrees")
    
    print("\n" + "="*50)
    print("3D COORDINATE TRANSFORMATION RESULTS")
    print("="*50)
    print(f"Large circle - Camera: {large_center_3d}")
    print(f"Large circle - World: {large_center_3d_world}")
    print(f"Small circle - Camera: {small_center_3d}")
    print(f"Small circle - World: {small_center_3d_world}")
    print(f"\n3D Displacement: {displacement_3d}")
    print(f"Rotation angle: {rot_angle:.2f} degrees")
    print("="*50)
    
    return displacement_3d, rot_angle


if __name__ == "__main__":
    main()