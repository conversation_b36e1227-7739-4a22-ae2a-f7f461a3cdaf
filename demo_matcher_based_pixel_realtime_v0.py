import numpy as np
import cv2
import torch
import sys
from pathlib import Path
from camera.HKcam.hk_camera_manager import HKCameraManager

from robo_ctl.UR.ur_robot_controller import URRobotController
from utils import pose_6d_to_matrix, matrix_to_axis_6d
from template_matcher.utils_rot import calculate_angle_with_horizontal
from template_matcher.utils_matcher import detect_circle_in_roi

# Add preprocess directory to Python path
sys.path.append(str(Path(__file__).resolve().parent / 'preprocess'))
from preprocess.infer_wrap import YOLOv8TRTDetector


def get_user_confirmation():
    """Get user confirmation to move the robot."""
    user_input = input("Move the robot? (y/n): ").lower()
    if user_input == 'n':
        print("Operation cancelled.")
        exit()
    elif user_input != 'y':
        print("Invalid input!")
        exit()
    print("Robot moving!")

def move_robot(use_rot, world_delta=None, rot_angle=None):
    """Move the robot based on rotation mode and calculated parameters."""
    with URRobotController("192.168.25.15") as robot:
        if use_rot:
            # Initial position
            take_pic_euler = np.array([0.76383, -0.24167, 0.36874, 180, -0.0, -90.0])
            take_pic_matrix = pose_6d_to_matrix(take_pic_euler, unit='m')
            take_pic_axis = matrix_to_axis_6d(take_pic_matrix, unit='m')

            # Translation to align centers
            take_pic_axis[0] -= 0.15
            robot.move_linear(take_pic_axis, 0.05, 0.05)

            take_pic_axis[0] += 0.15
            take_pic_axis[1] = -0.1
            take_pic_axis[2] = 0.332
            Rx, Ry, Rz = take_pic_euler[3], take_pic_euler[4] + rot_angle, take_pic_euler[5]
            target_euler = np.array([take_pic_axis[0], take_pic_axis[1], take_pic_axis[2], Rx, Ry, Rz])
            target_matrix = pose_6d_to_matrix(target_euler, unit='m')
            target_axis = matrix_to_axis_6d(target_matrix, unit='m')
            robot.move_linear(target_axis, 0.05, 0.05)
        else:
            position = robot.get_tcp_pose()
            position[0] -= 0.15
            robot.move_linear(position, 0.05, 0.05)

            position[0] += 0.12
            position[1] += world_delta[0]
            position[2] += world_delta[1]
            robot.move_linear(position, 0.05, 0.05)

def main():
    with HKCameraManager(index=0) as camera:
        scene_bgr = camera.capture_single_image(None)
        if scene_bgr is None:
            print("Failed to capture image from HK camera.")
            return

    # YOLO detector configuration
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    yolo_engine = 'preprocess/weights/yolov8_seatbelt_v1.engine'
    
    # Initialize YOLO detector
    detector = YOLOv8TRTDetector(yolo_engine, device)
    
    use_rot = False  # Set to True to enable rotation mode
    
    # Load scene image
    scene_gray = cv2.cvtColor(scene_bgr, cv2.COLOR_BGR2GRAY)
    
    # Perform YOLO detection
    detections = detector.predict(scene_bgr)
    
    # Initialize variables for ROI centers
    large_center = None
    small_center = None
    
    # Process detections
    for bbox, label in zip(detections['bboxes'], detections['labels']):
        # Convert bounding box coordinates to integers
        x1, y1, x2, y2 = map(int, bbox)
        
        # Extract ROI from scene image
        roi_gray = scene_gray[y1:y2, x1:x2]
        
        # Map labels to ROI types
        # label = 1 corresponds to large_roi
        # label = 2 corresponds to small_roi
        if label == 1:
            # Detect circles in large ROI
            large_circles = detect_circle_in_roi(roi_gray, x1, y1, "large")
            if large_circles:
                # Use the first detected circle
                large_center = (large_circles[0][0], large_circles[0][1])
                print(f"Large circle center: {large_center}, radius: {large_circles[0][2]}")
        elif label == 2:
            # Detect circles in small ROI
            small_circles = detect_circle_in_roi(roi_gray, x1, y1, "small")
            if small_circles:
                # Use the first detected circle
                small_center = (small_circles[0][0], small_circles[0][1])
                print(f"Small circle center: {small_center}, radius: {small_circles[0][2]}")
    
    # Check if both centers were detected
    if large_center is None or small_center is None:
        print("Could not detect both large and small circles.")
        return

    # Transformation matrix (camera to world)
    R_cam_to_world = np.array([
        [-3.05701394e-06, 6.36506259e-05, -3.98354809e-01],
        [-6.31956155e-05, -4.21291944e-06, 2.21344384e-01],
        [0, 0, 1]
    ])

    # Unused matrix - kept for compatibility
    R_world_to_cam = np.array([
        [ -1044.01138588, -15773.37975983,   3075.46206763],
        [ 15660.62279347,   -757.56429944,   6406.1670116 ],
        [0, 0, 1]
    ])

    large_world = R_cam_to_world @ np.array([large_center[0], large_center[1], 1])
    small_world = R_cam_to_world @ np.array([small_center[0], small_center[1], 1])
    world_delta = small_world - large_world

    print(f"Large world coordinates: {large_world}")
    print(f"World delta: dx=0, dy={world_delta[0]:.4f}, dz={world_delta[1]:.4f}")

    rot_angle = None
    if use_rot:
        tcp_xyz = np.array([0.76383, -0.24167, 0.36874])
        large_center_xyz = np.array([tcp_xyz[0], large_world[0], large_world[1]])
        initial_angle = calculate_angle_with_horizontal(tcp_xyz, large_center_xyz)
        rot_angle = - (90 + 28.44) - initial_angle
        print(f"Large center XYZ: {large_center_xyz}")
        print(f"Initial angle: {initial_angle:.4f} degrees")
        print("-" * 30)
        print(f"Rotation angle: {rot_angle:.2f} degrees")

    get_user_confirmation()
    move_robot(use_rot, world_delta, rot_angle)

if __name__ == "__main__":
    main()
