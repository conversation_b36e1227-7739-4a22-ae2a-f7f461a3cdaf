import cv2
import numpy as np
import matplotlib.pyplot as plt
import os

from robo_ctl.ur_robot_controller import URRobotController
from utils import pose_6d_to_matrix, matrix_to_axis_6d

from utils_rot import (
    calculate_angle_with_horizontal, 
    rotate_point_in_yz_plane
)

def preprocess_image(image, is_template=False):
    """Image preprocessing: grayscale + Gaussian blur + adaptive threshold"""
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)
    thresh = cv2.adaptiveThreshold(
        blurred, 255, 
        cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
        cv2.THRESH_BINARY_INV, 21, 10
    )
    
    if is_template:
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        thresh = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)
    
    return thresh


def template_matching_registration(template_path, image_path, save_dir="output", flag="small"):
    """Template matching + affine transformation registration (fixed version)"""
    
    # create save_dir if not exists
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)
    
    # 1. Load images
    template_bgr = cv2.imread(template_path)
    image_bgr = cv2.imread(image_path)
    
    # 2. Preprocessing
    template_gray = preprocess_image(template_bgr, is_template=True)
    image_gray = preprocess_image(image_bgr)
    
    # 3. Template matching
    result = cv2.matchTemplate(
        image_gray, template_gray, 
        cv2.TM_CCOEFF_NORMED
    )
    min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
    
    # 4. Determine matched region
    h, w = template_gray.shape[:2]
    top_left = max_loc
    bottom_right = (top_left[0] + w, top_left[1] + h)
    cv2.rectangle(image_bgr, top_left, bottom_right, (0 ,0 ,250 ), 3)
    
    image_gray = cv2.cvtColor(image_bgr, cv2.COLOR_BGR2GRAY)
    # Extract ROI from the original scene image (grayscale for HoughCircles)
    roi_gray = image_gray[top_left[1]:top_left[1] + h - 1, top_left[0]:top_left[0] + w - 1]


    if flag == "small":
        circles = cv2.HoughCircles(roi_gray, cv2.HOUGH_GRADIENT, dp=1.5, minDist=100,
                                        param1=100, param2=50, minRadius=70, maxRadius=90) # small circle
    elif flag == "large":
        circles = cv2.HoughCircles(roi_gray, cv2.HOUGH_GRADIENT, dp=1.5, minDist=100,
                                        param1=80, param2=50, minRadius=175, maxRadius=200) # large circle
    else:
        raise ValueError(f"Unsupported flag '{flag}'. Use 'small' or 'large'.")

    circles_res = []
    if circles is not None:
        circles = np.uint16(np.around(circles))
        for i in circles[0, :]:
            # Draw outer circle
            # Shift coordinates from ROI to the combined img_matches
            # Add template.shape[1] to x-coordinate because scene is to the right
            center_x = i[0] + top_left[0]
            center_y = i[1] + top_left[1]
            radius = i[2]
            cv2.circle(image_bgr, (center_x, center_y), radius, (0, 0, 255), 2) # Red circle
            # Draw center of the circle
            cv2.circle(image_bgr, (center_x, center_y), 2, (0, 255, 0), 3) # Green center
            print(f"Detected circle at ({center_x}, {center_y}) with radius {radius} within the bounded region.")
            circles_res.append((center_x, center_y))

    cv2.imwrite(f"{save_dir}/image_{flag}.png", image_bgr)

    return circles_res, roi_gray


def main():
    scene_path = "data/2d_images/Image_20250707113725896.bmp"
    template_path_1 = "data/2d_images/cropped-template_1.png"
    template_path_2 = "data/2d_images/cropped-template_2.png"
    template_path_3 = "data/2d_images/cropped-cropped-Image_20250721110604758.png"
    
    # large circle
    result_large_1, roi_gray_large = template_matching_registration(
        template_path=template_path_1,
        scene_path=scene_path,
        save_dir="output",
        flag="large",
    )
    # small circle
    result_small_2, _ = template_matching_registration(
        template_path=template_path_2,
        scene_path=scene_path,
        save_dir="output",
        flag="small",
    )

    result_large_3, _ = template_matching_registration(
        template_path=template_path_3,
        scene_path=scene_path,
        save_dir="output",
        flag="large",
    )


    x1, y1 = result_large_1[0]
    x2, y2 = result_small_2[0]
    print(f"Circle 1: ({x1}, {y1})")
    print(f"Circle 2: ({x2}, {y2})")

    dx_pixel = x2 - x1
    dy_pixel = y2 - y1
    print(f"Pixel coordinate: dx = {dx_pixel}, dy = {dy_pixel}")

    pt1_pixel = np.array([x1, y1, 1])
    pt2_pixel = np.array([x2, y2, 1])

    R_cam_to_world = np.array([
        [-3.05701394e-06, 6.36506259e-05, -3.98354809e-01],
        [-6.31956155e-05, -4.21291944e-06, 2.21344384e-01],
        [0, 0, 1]
    ])
    R_world_to_cam = np.array([
        [ -1044.01138588, -15773.37975983,   3075.46206763],
        [ 15660.62279347,   -757.56429944,   6406.1670116 ],
        [0, 0, 1]
    ])

    # R_cam_to_world = np.array([
    #     [0, 0, 666.878],
    #     [-4.25e-03, 6.576e-02, 805.73],
    #     [-6.51e-02, -4.387e-03, 1]
    # ])

    pt1_world = R_cam_to_world @ pt1_pixel
    pt2_world = R_cam_to_world @ pt2_pixel # [y, z, 1]
    d_world = pt2_world - pt1_world
    print(f"world coordinate: dx = 0, dy = {d_world[0]}, dz = {d_world[1]}")

    # line detection
    # vis, angles = detect_lines(roi_gray_large)
    # rotated_endpoint = calculate_rotated_vector_endpoint(angles[0], (x1, y1))
    # robot_rx_deg, robot_rx_rad, _ = calculate_robot_rx_from_pixel_rotation(
    #     p_target_pix=rotated_endpoint, p_center_pix=(x1, y1), angle_a_deg=angles[0], M=R_cam_to_world) 
    # print(f"robot_rx: {robot_rx_deg:.2f} degrees, {robot_rx_rad:.2f} rad")

    tcp_xyz = np.array([769.96, -253.86, 363.07]) # x, y, z
    large_circle_center_xyz = np.array([769.96, pt1_world[0], pt1_world[1]])
    initial_angle = calculate_angle_with_horizontal(tcp_xyz, large_circle_center_xyz)
    print(f"向量 TCP->P 与Y轴正方向的初始夹角: {initial_angle:.4f} 度")
    print("-" * 30)
    rot_angle = -(90 - 28.44) - initial_angle
    print(f"rot_angle: {rot_angle:.2f} degrees")
    
    new_large_circle_center_xyz = rotate_point_in_yz_plane(tcp_xyz, large_circle_center_xyz, initial_angle)
    dy = pt2_world[0] - new_large_circle_center_xyz[1]
    dz = pt2_world[1] - new_large_circle_center_xyz[2]
    print(f"dy: {dy:.2f} m, dz: {dz:.2f} m")


    # angle_rad, angle_deg = get_angle(int(cur_uv[0]), int(cur_uv[1]), int(x1), int(y1))
    # print(f"angle: {angle_deg:.2f} degrees")
    # rot_uv_angle = 28.44 - angle_deg
    # print(f"rot_uv_angle: {rot_uv_angle:.2f} degrees")
    # robot_rx_deg, robot_rx_rad, v_trans_base = calculate_robot_rx_from_pixel_rotation(
    # p_target_pix=cur_uv, p_center_pix=(x1, y1), angle_a_deg=rot_uv_angle, M=R_cam_to_world) 
    # print(f"robot_rx: {robot_rx_deg:.2f} degrees, {robot_rx_rad:.2f} rad, v_trans_base: {v_trans_base}")
    
    # move robot
    # with URRobotController("192.168.25.15") as robot:
    #     position_take_pic_euler = np.array([0.76996, -0.25386, 0.36307, 180, -0.0, -90.0]) # x, y, z, rx, ry, rz
    #     position_take_pic_matrix = pose_6d_to_matrix(position_take_pic_euler, unit='m')
    #     position_take_pic_axis = matrix_to_axis_6d(position_take_pic_matrix, unit='m')

    #     # =========== Translation:: make circle centers aligned ===========
    #     position_take_pic_axis[0] -= 0.15
    #     robot.move_linear(position_take_pic_axis, 0.05, 0.05)

    #     position_take_pic_axis[0] += 0.08
    #     position_take_pic_axis[1] += dy
    #     position_take_pic_axis[2] += dz
    #     # robot.move_linear(position_take_pic_axis, 0.05, 0.05)
    #     # =========== Rotation:: make angle aligned =======================
    #     Rx = position_take_pic_euler[3] - rot_angle
    #     Ry = position_take_pic_euler[4]
    #     Rz = position_take_pic_euler[5]
    #     target_base_euler = np.array([
    #         position_take_pic_axis[0], position_take_pic_axis[1], position_take_pic_axis[2], 
    #         Rx, Ry, Rz])
    #     target_base_matrix = pose_6d_to_matrix(target_base_euler, unit='m')
    #     target_base_axis = matrix_to_axis_6d(target_base_matrix, unit='m')
    #     robot.move_linear(target_base_axis, 0.05, 0.05)

    #     robot.disconnect()
    

if __name__ == "__main__":
    main()
