import cv2
import numpy as np
import math

def draw_line_and_angle(p1, p2, image_size=(3648, 6021, 3)):
    """
    Draw a line between two points on a blank image and calculate its angle relative to horizontal.

    Args:
        p1 (tuple): Coordinates of first point (x1, y1).
        p2 (tuple): Coordinates of second point (x2, y2).
        image_size (tuple): Image dimensions (height, width, channels).

    Returns:
        numpy.ndarray: Image with drawn line and angle.
        float: Calculated angle in degrees.
    """
    # Create blank black image
    img = np.zeros(image_size, dtype=np.uint8)

    # --- Drawing ---
    # 1. Draw main line (white)
    cv2.line(img, p1, p2, (255, 255, 255), 2)

    # 2. Draw horizontal reference line from p1 (gray)
    # Left horizontal line endpoint
    p_horizontal_left = (50, p1[1])
    cv2.line(img, p1, p_horizontal_left, (128, 128, 128), 1, cv2.LINE_AA)  # Use anti-aliasing for smoother thin lines

    # --- Angle calculation ---
    # Calculate x and y differences
    dx = p2[0] - p1[0]
    dy = p2[1] - p1[1]

    # Calculate angle in radians using atan2
    # In OpenCV coordinate system, y-axis is positive downward, so we use -dy to match standard math coordinates
    angle_rad = math.atan2(-dy, dx)

    # Convert radians to degrees and normalize to 0-360
    angle_deg = math.degrees(angle_rad) + 180

    # --- Display angle text ---
    text = f"Angle: {angle_deg:.2f} degrees"
    # Position text near p1
    text_position = (p1[0] + 15, p1[1] - 15)
    cv2.putText(img, text, text_position, cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)

    return img, angle_deg

# --- Main program ---
if __name__ == '__main__':
    # Define two pixel coordinates
    point1 = (3301, 1876)
    point2 = (2989, 2045)

    # Call function to generate image with line and get angle
    image_with_line, calculated_angle = draw_line_and_angle(point1, point2)

    # Print results to console
    print(f"Point 1: {point1}")
    print(f"Point 2: {point2}")
    print(f"Calculated angle: {calculated_angle:.2f} degrees")
    print("Press any key to exit...")

    cv2.imwrite("line_and_angle.png", image_with_line)
    # --- Display image ---
    window_name = 'Line and Angle'
    cv2.imshow(window_name, image_with_line)

    # Wait for key press then close window
    cv2.waitKey(0)
    cv2.destroyAllWindows()
