import math

def rotate_point_in_yz_plane(tcp_center, point_p, angle_degrees):
    """
    Rotate a point P around another point TCP within the given y-z plane.

    Args:
        tcp_center (tuple): Coordinates (x0, y0, z0) of the rotation center (TCP).
        point_p (tuple): Coordinates (x0, y1, z1) of the point P to be rotated.
        angle_degrees (float): Rotation angle in degrees. Positive values indicate counter-clockwise rotation.

    Returns:
        tuple: New coordinates (x_new, y_new, z_new) of the rotated point P'.
               Returns None if the input x-coordinates do not match.
    """
    x0, y0, z0 = tcp_center
    x1, y1, z1 = point_p

    # Check if both points lie on the same y-z plane
    if x0 != x1:
        print("Error: TCP center and point P must share the same x-coordinate for rotation in the y-z plane.")
        return None

    # --- Step 1: Translate the coordinate system so the rotation center (y0, z0) moves to origin (0,0) ---
    p_translated_y = y1 - y0
    p_translated_z = z1 - z0

    # --- Step 2: Convert rotation angle from degrees to radians ---
    # Python's math library uses radians for trigonometric functions
    angle_radians = math.radians(angle_degrees)

    # --- Step 3: Apply standard 2D rotation formulas ---
    # y' = y*cos(a) - z*sin(a)
    # z' = y*sin(a) + z*cos(a)
    p_rotated_y = p_translated_y * math.cos(angle_radians) - p_translated_z * math.sin(angle_radians)
    p_rotated_z = p_translated_y * math.sin(angle_radians) + p_translated_z * math.cos(angle_radians)

    # --- Step 4: Translate the coordinate system back ---
    # Move the rotated point from origin back to the original rotation center
    final_y = p_rotated_y + y0
    final_z = p_rotated_z + z0
    
    # The x-coordinate remains unchanged
    final_x = x0

    return (final_x, final_y, final_z)


def calculate_angle_with_horizontal(tcp_center, point_p):
    """
    Calculate the angle between the vector from TCP to P and the horizontal axis (positive Y-axis direction) within the y-z plane.

    Args:
        tcp_center (tuple): Coordinates (x0, y0, z0) of the TCP center point.
        point_p (tuple): Coordinates (x0, y1, z1) of point P.

    Returns:
        float: Computed angle in degrees. Range between -180 and 180 degrees.
               Returns None if the input x-coordinates do not match.
    """
    x0, y0, z0 = tcp_center
    x1, y1, z1 = point_p

    # Re-check if both points lie on the same y-z plane
    if x0 != x1:
        print("Error: TCP center and point P must share the same x-coordinate.")
        return None
    
    # Compute vector components
    delta_y = y1 - y0
    delta_z = z1 - z0

    # Use atan2 to compute radians. atan2(z, y) handles all quadrants
    angle_radians = math.atan2(delta_z, delta_y)

    # Convert radians to degrees
    angle_degrees = math.degrees(angle_radians)

    return angle_degrees