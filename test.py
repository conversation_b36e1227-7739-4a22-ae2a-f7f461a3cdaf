import cv2
import numpy as np
import matplotlib.pyplot as plt

# 1. Read image
img_path = 'data/2d_images/Image_20250707112929212.bmp'
img = cv2.imread(img_path)

if img is None:
    raise FileNotFoundError(f"无法读取图像文件: {img_path}")

# Create a figure to display all processing steps
plt.figure(figsize=(15, 10))

# 1. Show original image
plt.subplot(2, 4, 1)
plt.imshow(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))
plt.title('Original Image')
plt.axis('off')

# 2. Convert to grayscale
gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
plt.subplot(2, 4, 2)
plt.imshow(gray, cmap='gray')
plt.title('Grayscale Image')
plt.axis('off')

# 3. Apply Gaussian blur
blur = cv2.GaussianBlur(gray, (9,9), 2)      # Noise reduction
plt.subplot(2, 4, 3)
plt.imshow(blur, cmap='gray')
plt.title('Gaussian Blur')
plt.axis('off')

# 4. Apply CLAHE enhancement
clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
enh = clahe.apply(blur)                     # Contrast enhancement
plt.subplot(2, 4, 4)
plt.imshow(enh, cmap='gray')
plt.title('CLAHE Enhancement')
plt.axis('off')

# 5. Canny edge detection
edges = cv2.Canny(enh, 50, 150)               # Edge detection
plt.subplot(2, 4, 5)
plt.imshow(edges, cmap='gray')
plt.title('Canny Edge Detection')
plt.axis('off')

# 6. Morphological closing operation
kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE,(5,5))
edges = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel) # Closing operation
plt.subplot(2, 4, 6)
plt.imshow(edges, cmap='gray')
plt.title('Morphological Closing')
plt.axis('off')

# 7. Hough Circle Transform
circles = cv2.HoughCircles(enh,
            cv2.HOUGH_GRADIENT, dp=1.2, minDist=40,
            param1=150, param2=30,
            minRadius=20, maxRadius=60)

# 8. Visualize detected circles
img_with_circles = img.copy()
if circles is not None:
    circles = np.uint16(np.around(circles))
    for i in circles[0, :]:
        # Draw outer circle
        cv2.circle(img_with_circles, (i[0], i[1]), i[2], (0, 255, 0), 2)
        # Draw center point
        cv2.circle(img_with_circles, (i[0], i[1]), 2, (0, 0, 255), 3)
        # Add text annotation
        cv2.putText(img_with_circles, f'({i[0]}, {i[1]}) r={i[2]}', 
                   (i[0] - 50, i[1] - 20), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 0, 0), 1)

plt.subplot(2, 4, 7)
plt.imshow(cv2.cvtColor(img_with_circles, cv2.COLOR_BGR2RGB))
plt.title('Circle Detection Result')
plt.axis('off')

# Adjust subplot spacing
plt.tight_layout()
plt.show()
