# RVC相机集成指南

本文档详细说明了如何将RVC相机封装类集成到 `demo_grasp_realtime.py` 中，实现从静态文件读取到实时相机数据获取的转换。

## 修改概述

### 1. **导入RVC封装类**

在 `demo_grasp_realtime.py` 文件开头添加了RVC相机封装类的导入：

```python
# Import RVC camera wrapper
sys.path.append(str(Path(__file__).resolve().parent / 'camera' / 'RVC'))
from rvc_camera_wrapper import RVCCameraWrapper, CameraType, CameraID
```

### 2. **新增函数**

#### `capture_rvc_data(device_sn=None, exposure_time=100)`

**功能**: 从RVC相机捕获RGB图像和深度图像数据

**参数**:
- `device_sn`: 设备序列号（None表示使用第一个可用设备）
- `exposure_time`: 相机曝光时间

**返回值**: `(rgb_image, depth_image, camera_intrinsic)` 或 `(None, None, None)`

**关键特性**:
- 自动初始化X2相机，使用左相机
- 设置曝光时间
- 获取RGB图像并转换为RGB格式（从BGR转换）
- 获取深度图像并转换为numpy数组（单位：米）
- 获取相机内参矩阵
- 完善的错误处理和资源清理

#### `get_image_data_with_fallback(...)`

**功能**: 尝试从RVC相机获取数据，失败时回退到静态文件

**参数**:
- `device_sn`: 设备序列号
- `exposure_time`: 曝光时间
- `fallback_color`: 回退彩色图像路径
- `fallback_depth`: 回退深度图像路径

**返回值**: `(rgb_image, depth_image, camera_intrinsic, is_realtime)`

**关键特性**:
- 优先尝试RVC相机
- 失败时自动回退到静态文件
- 返回数据来源标识（实时/静态）
- 确保数据格式一致性

### 3. **主函数修改**

#### 原始代码
```python
# Path to files
template = 'data/templateA/templateA.ply' 
color = 'data/1.png'
depth = 'data/1.tif'

# ...

# yolo infer
pil_image = PIL.Image.open(color).convert('RGB')
cv_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)

# ...

# estimate pose
depth = cv2.imread(depth, cv2.IMREAD_ANYDEPTH)
rgb = cv2.cvtColor(cv_image, cv2.COLOR_BGR2RGB)

camera_intrinsic = np.array([
    [1577.85595703, 0, 717.44384765],
    [0, 1578.40002441, 565.813415527],
    [0, 0, 1]   
]) # RVC left instrinsic
```

#### 修改后代码
```python
# Path to template file
template = 'data/templateA/templateA.ply' 

# RVC camera configuration
device_sn = None  # Use None for first available camera
exposure_time = 100  # Adjust exposure time as needed

# ...

# Capture data from RVC camera with fallback
rgb_image, depth_image, camera_intrinsic, is_realtime = get_image_data_with_fallback(
    device_sn, exposure_time, 
    fallback_color='data/1.png', 
    fallback_depth='data/1.tif'
)

# Convert RGB image to PIL format for YOLO inference
pil_image = PIL.Image.fromarray(rgb_image)
cv_image = cv2.cvtColor(rgb_image, cv2.COLOR_RGB2BGR)

# ...

# Use captured data directly
depth = depth_image  # Already in correct format (meters)
rgb = rgb_image      # Already in RGB format
# camera_intrinsic already obtained from camera
```

## 数据格式兼容性

### RGB图像
- **原始**: PIL图像 → numpy数组 (RGB)
- **现在**: RVC相机 → numpy数组 (RGB) → PIL图像
- **兼容性**: ✅ 完全兼容

### 深度图像
- **原始**: TIFF文件 → cv2读取 → float32数组 (米)
- **现在**: RVC相机 → 临时TIFF → cv2读取 → float32数组 (米)
- **兼容性**: ✅ 完全兼容

### 相机内参
- **原始**: 硬编码的3x3 numpy数组
- **现在**: RVC相机获取 → 3x3 numpy数组（失败时使用默认值）
- **兼容性**: ✅ 完全兼容

## 使用方法

### 1. **基本使用**
```bash
python demo_grasp_realtime.py
```
- 自动尝试连接第一个可用的RVC相机
- 失败时自动回退到静态文件

### 2. **指定相机设备**
修改 `main()` 函数中的 `device_sn` 参数：
```python
device_sn = "M2GM012W019"  # 替换为你的相机序列号
```

### 3. **调整曝光时间**
修改 `main()` 函数中的 `exposure_time` 参数：
```python
exposure_time = 50  # 调整曝光时间
```

## 错误处理

### 1. **相机连接失败**
- 自动回退到静态文件
- 显示详细错误信息
- 程序继续正常运行

### 2. **数据捕获失败**
- 重试机制（在封装类中）
- 详细的错误日志
- 优雅的失败处理

### 3. **数据格式错误**
- 自动格式转换
- 数据类型验证
- 默认值回退

## 测试和验证

### 1. **运行集成测试**
```bash
python test_rvc_integration.py
```

### 2. **测试内容**
- RVC相机基本功能
- 数据捕获函数
- 回退机制
- 数据格式兼容性

### 3. **预期输出**
```
RVC Camera Integration Test
==================================================

✓ Successfully imported RVC camera wrapper

=== Testing RVC Camera Basic Functionality ===
✓ Camera initialized successfully
Device: RVC-X2
Serial: M2GM012W019
✓ Capture successful
✓ RGB image captured: (1080, 1440, 3), dtype: uint8
✓ Depth map captured
✓ Camera intrinsic parameters: (3, 3)

=== Testing Capture Function ===
✓ Capture function successful
RGB image shape: (1080, 1440, 3)
Depth image shape: (1080, 1440)
Camera intrinsic shape: (3, 3)

=== Testing Fallback Function ===
✓ Fallback function successful
🎥 Using real-time data from RVC camera

==================================================
Test Summary:
RVC Camera Basic: PASSED
Capture Function: PASSED
Fallback Function: PASSED

Overall: 3/3 tests passed
🎉 All tests passed! RVC integration is working correctly.
```

## 故障排除

### 1. **相机未检测到**
- 检查相机连接和电源
- 确认PyRVC库正确安装
- 检查设备权限

### 2. **导入错误**
- 确认RVC封装类文件存在
- 检查Python路径设置
- 验证依赖库安装

### 3. **数据格式问题**
- 检查深度图像单位（应为米）
- 验证RGB图像格式
- 确认相机内参矩阵

### 4. **性能问题**
- 调整曝光时间
- 优化图像分辨率
- 检查系统资源

## 配置选项

### 相机配置
```python
# 在main()函数中修改这些参数
device_sn = None          # 设备序列号
exposure_time = 100       # 曝光时间
camera_type = CameraType.X2  # 相机类型
camera_id = CameraID.LEFT    # 相机ID
```

### 回退配置
```python
# 修改回退文件路径
fallback_color = 'data/1.png'
fallback_depth = 'data/1.tif'
```

## 优势

1. **实时性**: 从静态文件转为实时相机数据
2. **可靠性**: 完善的回退机制确保程序稳定运行
3. **兼容性**: 保持原有数据格式和处理流程
4. **灵活性**: 支持多种配置选项
5. **可维护性**: 清晰的代码结构和错误处理

## 注意事项

1. **相机权限**: 确保有访问相机设备的权限
2. **资源管理**: 使用上下文管理器自动管理相机资源
3. **数据同步**: RGB和深度图像来自同一次捕获，确保数据同步
4. **性能考虑**: 实时捕获可能比文件读取稍慢
5. **错误恢复**: 相机断开后需要重新初始化
