"""
Camera configuration settings for the Percipio Camera wrapper.

This module contains configuration options and utility functions for customizing
camera behavior.
"""

from dataclasses import dataclass
from typing import Optional, Dict, Any


@dataclass
class CameraConfig:
    """Configuration settings for the Percipio camera."""
    
    # Device selection
    device_index: int = 0
    color_format_index: int = 2
    depth_format_index: int = 0
    
    # Capture settings
    default_timeout_ms: int = 2000
    align_depth_to_color: bool = True
    
    # Display settings
    show_by_default: bool = False
    save_by_default: bool = False
    
    # Save settings
    save_directory: str = "capture"
    save_color_images: bool = True
    save_depth_images: bool = True
    save_aligned_depth: bool = True
    save_point_clouds: bool = True
    
    # Image format settings
    color_image_format: str = "png"  # png, jpg
    depth_image_format: str = "png"  # png, tiff
    point_cloud_format: str = "npy"  # npy, ply
    
    # Display window settings
    window_names: Dict[str, str] = None
    
    def __post_init__(self):
        if self.window_names is None:
            self.window_names = {
                'color': 'Color Image',
                'depth': 'Depth Image',
                'aligned_depth': 'Aligned Depth Image'
            }


# Default configuration
DEFAULT_CONFIG = CameraConfig()


# Predefined configurations for different use cases
CONFIGS = {
    'default': CameraConfig(),
    
    'high_quality': CameraConfig(
        color_format_index=0,  # Highest resolution color
        depth_format_index=0,  # Highest resolution depth
        default_timeout_ms=5000,
        color_image_format='png',
        depth_image_format='tiff'
    ),
    
    'fast_capture': CameraConfig(
        color_format_index=3,  # Lower resolution for speed
        depth_format_index=1,  # Lower resolution for speed
        default_timeout_ms=1000,
        align_depth_to_color=False,  # Skip alignment for speed
        save_aligned_depth=False
    ),
    
    'save_all': CameraConfig(
        save_by_default=True,
        save_color_images=True,
        save_depth_images=True,
        save_aligned_depth=True,
        save_point_clouds=True
    ),
    
    'display_only': CameraConfig(
        show_by_default=True,
        save_by_default=False,
        save_color_images=False,
        save_depth_images=False,
        save_aligned_depth=False,
        save_point_clouds=False
    ),
    
    'research': CameraConfig(
        color_format_index=0,  # Highest quality
        depth_format_index=0,
        default_timeout_ms=3000,
        save_by_default=True,
        color_image_format='png',
        depth_image_format='tiff',
        point_cloud_format='ply'
    )
}


def get_config(name: str = 'default') -> CameraConfig:
    """
    Get a predefined configuration.
    
    Args:
        name: Name of the configuration ('default', 'high_quality', 'fast_capture', 
              'save_all', 'display_only', 'research')
    
    Returns:
        CameraConfig object
    
    Raises:
        ValueError: If configuration name is not found
    """
    if name not in CONFIGS:
        available = ', '.join(CONFIGS.keys())
        raise ValueError(f"Configuration '{name}' not found. Available: {available}")
    
    return CONFIGS[name]


def list_configs() -> Dict[str, str]:
    """
    List available configurations with descriptions.
    
    Returns:
        Dictionary mapping config names to descriptions
    """
    descriptions = {
        'default': 'Standard configuration with balanced settings',
        'high_quality': 'Maximum quality settings for best image quality',
        'fast_capture': 'Optimized for speed with lower resolution',
        'save_all': 'Automatically save all captured data',
        'display_only': 'Show images without saving',
        'research': 'High-quality settings optimized for research use'
    }
    return descriptions


def print_config_info():
    """Print information about available configurations."""
    print("Available Camera Configurations:")
    print("=" * 40)
    
    descriptions = list_configs()
    for name, desc in descriptions.items():
        config = CONFIGS[name]
        print(f"\n{name.upper()}:")
        print(f"  Description: {desc}")
        print(f"  Color format index: {config.color_format_index}")
        print(f"  Depth format index: {config.depth_format_index}")
        print(f"  Timeout: {config.default_timeout_ms}ms")
        print(f"  Align depth: {config.align_depth_to_color}")
        print(f"  Save by default: {config.save_by_default}")


def create_custom_config(**kwargs) -> CameraConfig:
    """
    Create a custom configuration by modifying the default.
    
    Args:
        **kwargs: Configuration parameters to override
    
    Returns:
        Custom CameraConfig object
    
    Example:
        config = create_custom_config(
            device_index=1,
            save_by_default=True,
            default_timeout_ms=5000
        )
    """
    # Start with default config
    config_dict = DEFAULT_CONFIG.__dict__.copy()
    
    # Update with custom parameters
    config_dict.update(kwargs)
    
    return CameraConfig(**config_dict)


if __name__ == "__main__":
    # Print configuration information
    print_config_info()
    
    print("\n" + "=" * 40)
    print("Example: Creating custom configuration")
    
    # Example of creating a custom configuration
    custom_config = create_custom_config(
        device_index=0,
        save_by_default=True,
        default_timeout_ms=3000,
        save_directory="my_captures"
    )
    
    print(f"Custom config created:")
    print(f"  Device index: {custom_config.device_index}")
    print(f"  Save by default: {custom_config.save_by_default}")
    print(f"  Timeout: {custom_config.default_timeout_ms}ms")
    print(f"  Save directory: {custom_config.save_directory}")
