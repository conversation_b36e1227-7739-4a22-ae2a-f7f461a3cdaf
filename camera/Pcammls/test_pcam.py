#!/usr/bin/env python3
"""
Test script for the Percipio Camera wrapper.

This script demonstrates how to use the PercipioCamera class for various tasks.
"""

import sys
import os
import time

# Add the current directory to path to import pcam
sys.path.append(os.path.dirname(__file__))

from pcam import PercipioCamera, CaptureData
import cv2


def test_camera_info():
    """Test camera information retrieval."""
    print("=== Testing Camera Info ===")
    
    try:
        with PercipioCamera() as camera:
            # Print all camera information
            camera.print_camera_info()
            
            # Get specific information
            resolutions = camera.get_resolution()
            print(f"\nResolutions: {resolutions}")
            
            intrinsics = camera.get_intrinsics()
            print(f"Intrinsics available: {list(intrinsics.keys())}")
            
            return True
            
    except Exception as e:
        print(f"Error testing camera info: {e}")
        return False


def test_single_capture():
    """Test single frame capture."""
    print("\n=== Testing Single Capture ===")
    
    try:
        with PercipioCamera() as camera:
            # Capture one frame
            data = camera.capture(show=False, save=False)
            
            print(f"Capture timestamp: {data.timestamp}")
            
            if data.color_image is not None:
                print(f"Color image captured: {data.color_image.shape}")
            else:
                print("No color image captured")
                
            if data.depth_image is not None:
                print(f"Depth image captured: {data.depth_image.shape}")
            else:
                print("No depth image captured")
                
            if data.aligned_depth_image is not None:
                print(f"Aligned depth image captured: {data.aligned_depth_image.shape}")
            else:
                print("No aligned depth image captured")
                
            if data.point_cloud is not None:
                print(f"Point cloud captured: {data.point_cloud.shape}")
            else:
                print("No point cloud captured")
            
            return True
            
    except Exception as e:
        print(f"Error testing single capture: {e}")
        return False


def test_live_capture():
    """Test live capture with display."""
    print("\n=== Testing Live Capture ===")
    print("Press 'q' to quit, 's' to save current frame")
    
    try:
        with PercipioCamera() as camera:
            frame_count = 0
            
            while True:
                # Capture frame
                data = camera.capture(show=True, save=False)
                frame_count += 1
                
                # Print frame info every 30 frames
                if frame_count % 30 == 0:
                    print(f"Frame {frame_count} captured")
                
                # Handle keyboard input
                key = cv2.waitKey(30) & 0xFF
                if key == ord('q'):
                    print("Quitting...")
                    break
                elif key == ord('s'):
                    print("Saving current frame...")
                    camera.capture(show=False, save=True)
                    print("Frame saved!")
            
            return True
            
    except Exception as e:
        print(f"Error testing live capture: {e}")
        return False
    finally:
        cv2.destroyAllWindows()


def test_save_functionality():
    """Test data saving functionality."""
    print("\n=== Testing Save Functionality ===")
    
    try:
        with PercipioCamera() as camera:
            # Capture and save data
            print("Capturing and saving data...")
            data = camera.capture(show=False, save=True)
            
            print("Data saved successfully!")
            print(f"Timestamp: {data.timestamp}")
            
            return True
            
    except Exception as e:
        print(f"Error testing save functionality: {e}")
        return False


def main():
    """Run all tests."""
    print("Percipio Camera Wrapper Test Suite")
    print("=" * 40)
    
    tests = [
        ("Camera Info", test_camera_info),
        ("Single Capture", test_single_capture),
        ("Save Functionality", test_save_functionality),
        ("Live Capture", test_live_capture),  # This should be last as it's interactive
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\nRunning {test_name} test...")
        try:
            results[test_name] = test_func()
        except KeyboardInterrupt:
            print(f"\n{test_name} test interrupted by user")
            results[test_name] = False
            break
        except Exception as e:
            print(f"Unexpected error in {test_name} test: {e}")
            results[test_name] = False
    
    # Print results summary
    print("\n" + "=" * 40)
    print("Test Results Summary:")
    for test_name, passed in results.items():
        status = "PASSED" if passed else "FAILED"
        print(f"  {test_name}: {status}")
    
    total_tests = len(results)
    passed_tests = sum(results.values())
    print(f"\nTotal: {passed_tests}/{total_tests} tests passed")
    
    return passed_tests == total_tests


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
