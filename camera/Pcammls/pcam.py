"""
Percipio Camera Wrapper - A high-level object-oriented interface for Percipio cameras.

This module provides a simplified, context-manager-based interface for working with
Percipio depth cameras, including image acquisition, point cloud generation, and
camera parameter access.

Author: Generated based on Percipio SDK examples
Date: 2025-08-22
"""

import os
import sys
import time
from typing import Optional, Tuple, Dict, Any, Union
from dataclasses import dataclass
from pathlib import Path

import cv2
import numpy as np

# Import Percipio SDK
try:
    sys.path.append(os.path.join(os.path.dirname(__file__), 'pcammls_py39'))
    import pcammls
    from pcammls import *
except ImportError as e:
    print(f"Warning: Failed to import Percipio SDK: {e}")
    print("Make sure the pcammls_py39 directory contains the required SDK files.")
    # Define placeholder constants for IDE
    PERCIPIO_STREAM_COLOR = 1
    PERCIPIO_STREAM_DEPTH = 2
    TY_EVENT_DEVICE_OFFLINE = 3

    class PercipioSDK:
        pass

    class image_data:
        def as_nparray(self):
            return np.array([])

    class pointcloud_data_list:
        def as_nparray(self):
            return np.array([])


@dataclass
class CameraInfo:
    """Camera information and calibration data."""
    width: int
    height: int
    intrinsic: Any  # Camera intrinsic matrix
    extrinsic: Any  # Camera extrinsic matrix
    distortion: Any  # Distortion coefficients
    scale_unit: float = 1.0


@dataclass
class CaptureData:
    """Container for captured camera data."""
    color_image: Optional[np.ndarray] = None
    depth_image: Optional[np.ndarray] = None
    aligned_depth_image: Optional[np.ndarray] = None
    point_cloud: Optional[np.ndarray] = None
    timestamp: float = 0.0


class PercipioDeviceEvent(pcammls.DeviceEvent):
    """Device event handler for camera disconnection detection."""

    def __init__(self):
        pcammls.DeviceEvent.__init__(self)
        self.offline = False

    def run(self, handle, eventID):
        _ = handle  # Unused parameter
        if eventID == TY_EVENT_DEVICE_OFFLINE:
            print("=== Event Callback: Device Offline!")
            self.offline = True
        return 0

    def is_offline(self):
        return self.offline


class PercipioCamera:
    """
    High-level interface for Percipio depth cameras.

    This class provides a context manager interface for working with Percipio cameras,
    supporting image acquisition, point cloud generation, and camera parameter access.

    Example:
        with PercipioCamera() as camera:
            data = camera.capture(show=True, save=False)
            if data.color_image is not None:
                print(f"Captured color image: {data.color_image.shape}")
    """

    def __init__(self, device_index: int = 0, color_format_index: int = 2,
                 depth_format_index: int = 0):
        """
        Initialize the camera wrapper.

        Args:
            device_index: Index of the device to use (default: 0 for first device)
            color_format_index: Index of color format to use (default: 2)
            depth_format_index: Index of depth format to use (default: 0)
        """
        self.device_index = device_index
        self.color_format_index = color_format_index
        self.depth_format_index = depth_format_index

        # SDK and device handles
        self.sdk = None
        self.handle = None
        self.event_handler = None

        # Camera information
        self.color_info: Optional[CameraInfo] = None
        self.depth_info: Optional[CameraInfo] = None

        # Stream status
        self.streams_enabled = False
        self.streams_started = False

        # Image buffers
        self._color_buffer = None
        self._depth_buffer = None
        self._depth_render_buffer = None
        self._registration_buffer = None
        self._pointcloud_buffer = None

    def __enter__(self):
        """Context manager entry."""
        self._initialize()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit with proper cleanup."""
        _ = exc_type, exc_val, exc_tb  # Unused parameters
        self._cleanup()

    def _initialize(self):
        """Initialize the camera and configure streams."""
        try:
            # Initialize SDK
            self.sdk = PercipioSDK()

            # List and select device
            dev_list = self.sdk.ListDevice()
            if len(dev_list) == 0:
                raise RuntimeError("No Percipio devices found")

            if self.device_index >= len(dev_list):
                raise ValueError(f"Device index {self.device_index} out of range. "
                               f"Found {len(dev_list)} devices.")

            device_info = dev_list[self.device_index]
            print(f"Using device: {device_info.id} - {device_info.iface.id}")

            # Open device
            self.handle = self.sdk.Open(device_info.id)
            if not self.sdk.isValidHandle(self.handle):
                err = self.sdk.TYGetLastErrorCodedescription()
                raise RuntimeError(f"Failed to open device: {err}")

            # Setup event handler
            self.event_handler = PercipioDeviceEvent()
            self.sdk.DeviceRegiststerCallBackEvent(self.event_handler)

            # Configure streams
            self._configure_streams()

            # Load default parameters
            err = self.sdk.DeviceLoadDefaultParameters(self.handle)
            if err:
                print(f"Warning: Failed to load default parameters: "
                      f"{self.sdk.TYGetLastErrorCodedescription()}")

            # Initialize image buffers
            self._initialize_buffers()

            print("Camera initialized successfully")

        except Exception as e:
            self._cleanup()
            raise RuntimeError(f"Failed to initialize camera: {e}")

    def _configure_streams(self):
        """Configure color and depth streams."""
        # Configure color stream
        color_fmt_list = self.sdk.DeviceStreamFormatDump(self.handle, PERCIPIO_STREAM_COLOR)
        if len(color_fmt_list) > 0:
            if self.color_format_index >= len(color_fmt_list):
                self.color_format_index = 0
                print(f"Warning: Color format index out of range, using index 0")

            selected_fmt = color_fmt_list[self.color_format_index]
            print(f"Color format: {selected_fmt.getDesc()} "
                  f"[{self.sdk.Width(selected_fmt)}x{self.sdk.Height(selected_fmt)}]")

            self.sdk.DeviceStreamFormatConfig(self.handle, PERCIPIO_STREAM_COLOR, selected_fmt)

            # Get color calibration data
            color_calib = self.sdk.DeviceReadCalibData(self.handle, PERCIPIO_STREAM_COLOR)
            self.color_info = CameraInfo(
                width=color_calib.Width(),
                height=color_calib.Height(),
                intrinsic=color_calib.Intrinsic(),
                extrinsic=color_calib.Extrinsic(),
                distortion=color_calib.Distortion()
            )

        # Configure depth stream
        depth_fmt_list = self.sdk.DeviceStreamFormatDump(self.handle, PERCIPIO_STREAM_DEPTH)
        if len(depth_fmt_list) > 0:
            if self.depth_format_index >= len(depth_fmt_list):
                self.depth_format_index = 0
                print(f"Warning: Depth format index out of range, using index 0")

            selected_fmt = depth_fmt_list[self.depth_format_index]
            print(f"Depth format: {selected_fmt.getDesc()} "
                  f"[{self.sdk.Width(selected_fmt)}x{self.sdk.Height(selected_fmt)}]")

            self.sdk.DeviceStreamFormatConfig(self.handle, PERCIPIO_STREAM_DEPTH, selected_fmt)

            # Get depth calibration data and scale unit
            depth_calib = self.sdk.DeviceReadCalibData(self.handle, PERCIPIO_STREAM_DEPTH)
            scale_unit = self.sdk.DeviceReadCalibDepthScaleUnit(self.handle)

            self.depth_info = CameraInfo(
                width=depth_calib.Width(),
                height=depth_calib.Height(),
                intrinsic=depth_calib.Intrinsic(),
                extrinsic=depth_calib.Extrinsic(),
                distortion=depth_calib.Distortion(),
                scale_unit=scale_unit
            )

    def _initialize_buffers(self):
        """Initialize image processing buffers."""
        self._color_buffer = image_data()
        self._depth_buffer = image_data()
        self._depth_render_buffer = image_data()
        self._registration_buffer = image_data()
        self._pointcloud_buffer = pointcloud_data_list()

    def _cleanup(self):
        """Clean up resources."""
        try:
            if self.streams_started and self.sdk and self.handle:
                self.sdk.DeviceStreamOff(self.handle)
                self.streams_started = False

            if self.handle and self.sdk:
                self.sdk.Close(self.handle)
                self.handle = None

            self.sdk = None
            self.event_handler = None
            self.streams_enabled = False

        except Exception as e:
            print(f"Warning: Error during cleanup: {e}")

    def start_streams(self):
        """Start camera streams."""
        if not self.handle:
            raise RuntimeError("Camera not initialized")

        if self.streams_started:
            return

        # Enable streams
        stream_flags = 0
        if self.color_info:
            stream_flags |= PERCIPIO_STREAM_COLOR
        if self.depth_info:
            stream_flags |= PERCIPIO_STREAM_DEPTH

        if stream_flags == 0:
            raise RuntimeError("No streams available")

        err = self.sdk.DeviceStreamEnable(self.handle, stream_flags)
        if err:
            raise RuntimeError(f"Failed to enable streams: {err}")

        self.streams_enabled = True

        # Start streaming
        self.sdk.DeviceStreamOn(self.handle)
        self.streams_started = True
        print("Camera streams started")

    def stop_streams(self):
        """Stop camera streams."""
        if self.streams_started and self.sdk and self.handle:
            self.sdk.DeviceStreamOff(self.handle)
            self.streams_started = False
            print("Camera streams stopped")

    def is_connected(self) -> bool:
        """Check if camera is still connected."""
        if not self.event_handler:
            return False
        return not self.event_handler.is_offline()

    def capture(self, show: bool = False, save: bool = False,
                timeout_ms: int = 2000, align_depth_to_color: bool = True) -> CaptureData:
        """
        Capture images and point cloud data.

        Args:
            show: Whether to display images using OpenCV
            save: Whether to save captured data to files
            timeout_ms: Timeout for frame capture in milliseconds
            align_depth_to_color: Whether to align depth image to color coordinate

        Returns:
            CaptureData object containing captured images and point cloud
        """
        if not self.streams_started:
            self.start_streams()

        if not self.is_connected():
            raise RuntimeError("Camera disconnected")

        # Capture frames
        image_list = self.sdk.DeviceStreamRead(self.handle, timeout_ms)
        if len(image_list) == 0:
            raise RuntimeError("No frames captured within timeout")

        # Initialize capture data
        capture_data = CaptureData(timestamp=time.time())

        # Process captured frames
        color_frame = None
        depth_frame = None

        for frame in image_list:
            if frame.streamID == PERCIPIO_STREAM_COLOR:
                color_frame = frame
            elif frame.streamID == PERCIPIO_STREAM_DEPTH:
                depth_frame = frame

        # Process color image
        if color_frame is not None and self.color_info is not None:
            self.sdk.DeviceStreamImageDecode(color_frame, self._color_buffer)
            capture_data.color_image = self._color_buffer.as_nparray().copy()

        # Process depth image
        if depth_frame is not None and self.depth_info is not None:
            # Render depth for visualization
            self.sdk.DeviceStreamDepthRender(depth_frame, self._depth_render_buffer)
            capture_data.depth_image = self._depth_render_buffer.as_nparray().copy()

            # Generate point cloud
            self.sdk.DeviceStreamMapDepthImageToPoint3D(
                depth_frame,
                self.sdk.DeviceReadCalibData(self.handle, PERCIPIO_STREAM_DEPTH),
                self.depth_info.scale_unit,
                self._pointcloud_buffer
            )
            capture_data.point_cloud = self._pointcloud_buffer.as_nparray().copy()

            # Align depth to color if both streams available and requested
            if (color_frame is not None and self.color_info is not None and
                align_depth_to_color):

                self.sdk.DeviceStreamMapDepthImageToColorCoordinate(
                    self.sdk.DeviceReadCalibData(self.handle, PERCIPIO_STREAM_DEPTH),
                    depth_frame,
                    self.depth_info.scale_unit,
                    self.sdk.DeviceReadCalibData(self.handle, PERCIPIO_STREAM_COLOR),
                    self.color_info.width,
                    self.color_info.height,
                    self._registration_buffer
                )

                # Render aligned depth
                aligned_depth_render = image_data()
                self.sdk.DeviceStreamDepthRender(self._registration_buffer, aligned_depth_render)
                capture_data.aligned_depth_image = aligned_depth_render.as_nparray().copy()

        # Display images if requested
        if show:
            self._display_images(capture_data)

        # Save data if requested
        if save:
            self._save_data(capture_data)

        return capture_data

    def _display_images(self, data: CaptureData):
        """Display captured images using OpenCV."""
        if data.color_image is not None:
            cv2.imshow("Color Image", data.color_image)

        if data.depth_image is not None:
            cv2.imshow("Depth Image", data.depth_image)

        if data.aligned_depth_image is not None:
            cv2.imshow("Aligned Depth Image", data.aligned_depth_image)

        cv2.waitKey(1)  # Non-blocking wait to update windows

    def _save_data(self, data: CaptureData, base_path: str = "capture"):
        """Save captured data to files."""
        timestamp_str = str(int(data.timestamp))
        base_path = Path(base_path)
        base_path.mkdir(exist_ok=True)

        if data.color_image is not None:
            color_path = base_path / f"color_{timestamp_str}.png"
            cv2.imwrite(str(color_path), data.color_image)
            print(f"Saved color image: {color_path}")

        if data.depth_image is not None:
            depth_path = base_path / f"depth_{timestamp_str}.png"
            cv2.imwrite(str(depth_path), data.depth_image)
            print(f"Saved depth image: {depth_path}")

        if data.aligned_depth_image is not None:
            aligned_path = base_path / f"aligned_depth_{timestamp_str}.png"
            cv2.imwrite(str(aligned_path), data.aligned_depth_image)
            print(f"Saved aligned depth image: {aligned_path}")

        if data.point_cloud is not None:
            pc_path = base_path / f"pointcloud_{timestamp_str}.npy"
            np.save(str(pc_path), data.point_cloud)
            print(f"Saved point cloud: {pc_path}")

    def get_color_camera_info(self) -> Optional[CameraInfo]:
        """Get color camera calibration information."""
        return self.color_info

    def get_depth_camera_info(self) -> Optional[CameraInfo]:
        """Get depth camera calibration information."""
        return self.depth_info

    def get_resolution(self) -> Dict[str, Tuple[int, int]]:
        """
        Get camera resolutions.

        Returns:
            Dictionary with 'color' and 'depth' resolutions as (width, height) tuples
        """
        result = {}
        if self.color_info:
            result['color'] = (self.color_info.width, self.color_info.height)
        if self.depth_info:
            result['depth'] = (self.depth_info.width, self.depth_info.height)
        return result

    def get_intrinsics(self) -> Dict[str, Any]:
        """
        Get camera intrinsic parameters.

        Returns:
            Dictionary with 'color' and 'depth' intrinsic matrices
        """
        result = {}
        if self.color_info:
            result['color'] = self.color_info.intrinsic
        if self.depth_info:
            result['depth'] = self.depth_info.intrinsic
        return result

    def get_extrinsics(self) -> Dict[str, Any]:
        """
        Get camera extrinsic parameters.

        Returns:
            Dictionary with 'color' and 'depth' extrinsic matrices
        """
        result = {}
        if self.color_info:
            result['color'] = self.color_info.extrinsic
        if self.depth_info:
            result['depth'] = self.depth_info.extrinsic
        return result

    def get_distortion_coefficients(self) -> Dict[str, Any]:
        """
        Get camera distortion coefficients.

        Returns:
            Dictionary with 'color' and 'depth' distortion coefficients
        """
        result = {}
        if self.color_info:
            result['color'] = self.color_info.distortion
        if self.depth_info:
            result['depth'] = self.depth_info.distortion
        return result

    def print_camera_info(self):
        """Print detailed camera information."""
        print("\n=== Camera Information ===")

        if self.color_info:
            print(f"\nColor Camera:")
            print(f"  Resolution: {self.color_info.width}x{self.color_info.height}")
            print(f"  Intrinsic: {self.color_info.intrinsic}")
            print(f"  Extrinsic: {self.color_info.extrinsic}")
            print(f"  Distortion: {self.color_info.distortion}")

        if self.depth_info:
            print(f"\nDepth Camera:")
            print(f"  Resolution: {self.depth_info.width}x{self.depth_info.height}")
            print(f"  Scale Unit: {self.depth_info.scale_unit}")
            print(f"  Intrinsic: {self.depth_info.intrinsic}")
            print(f"  Extrinsic: {self.depth_info.extrinsic}")
            print(f"  Distortion: {self.depth_info.distortion}")


def demo_basic_usage():
    """Demonstrate basic camera usage."""
    print("=== Percipio Camera Demo ===")

    try:
        with PercipioCamera() as camera:
            # Print camera information
            camera.print_camera_info()

            print("\nCapturing frames... Press 'q' to quit")

            while True:
                # Capture data
                data = camera.capture(show=True, save=False)

                # Print capture info
                if data.color_image is not None:
                    print(f"Color image shape: {data.color_image.shape}")
                if data.depth_image is not None:
                    print(f"Depth image shape: {data.depth_image.shape}")
                if data.point_cloud is not None:
                    print(f"Point cloud shape: {data.point_cloud.shape}")

                # Check for quit
                key = cv2.waitKey(30) & 0xFF
                if key == ord('q'):
                    break

    except Exception as e:
        print(f"Error: {e}")
    finally:
        cv2.destroyAllWindows()


def demo_save_data():
    """Demonstrate saving captured data."""
    print("=== Save Data Demo ===")

    try:
        with PercipioCamera() as camera:
            print("Capturing and saving data...")

            # Capture and save one frame
            _ = camera.capture(show=False, save=True)

            print("Data saved successfully!")

    except Exception as e:
        print(f"Error: {e}")


if __name__ == "__main__":
    # Run basic demo
    demo_basic_usage()