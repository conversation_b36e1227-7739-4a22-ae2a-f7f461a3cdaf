#!/usr/bin/env python3
"""
Example usage of the Percipio Camera wrapper.

This script demonstrates various ways to use the PercipioCamera class.
"""

import sys
import os
import time

# Add the current directory to path to import pcam
sys.path.append(os.path.dirname(__file__))

from pcam import PercipioCamera
import cv2
import numpy as np


def example_basic_capture():
    """Example 1: Basic image capture."""
    print("=== Example 1: Basic Image Capture ===")
    
    try:
        with PercipioCamera() as camera:
            # Capture a single frame
            data = camera.capture(show=False, save=False)
            
            print(f"Capture successful at timestamp: {data.timestamp}")
            
            if data.color_image is not None:
                print(f"Color image: {data.color_image.shape}, dtype: {data.color_image.dtype}")
            
            if data.depth_image is not None:
                print(f"Depth image: {data.depth_image.shape}, dtype: {data.depth_image.dtype}")
            
            if data.point_cloud is not None:
                print(f"Point cloud: {data.point_cloud.shape}, dtype: {data.point_cloud.dtype}")
                
    except Exception as e:
        print(f"Error in basic capture: {e}")


def example_camera_info():
    """Example 2: Accessing camera information."""
    print("\n=== Example 2: Camera Information ===")
    
    try:
        with PercipioCamera() as camera:
            # Print comprehensive camera info
            camera.print_camera_info()
            
            # Access specific information
            resolutions = camera.get_resolution()
            print(f"\nResolutions: {resolutions}")
            
            # Get camera parameters
            color_info = camera.get_color_camera_info()
            depth_info = camera.get_depth_camera_info()
            
            if color_info:
                print(f"Color camera: {color_info.width}x{color_info.height}")
            
            if depth_info:
                print(f"Depth camera: {depth_info.width}x{depth_info.height}")
                print(f"Depth scale unit: {depth_info.scale_unit}")
                
    except Exception as e:
        print(f"Error accessing camera info: {e}")


def example_live_preview():
    """Example 3: Live preview with user interaction."""
    print("\n=== Example 3: Live Preview ===")
    print("Controls:")
    print("  'q' - Quit")
    print("  's' - Save current frame")
    print("  'i' - Print camera info")
    
    try:
        with PercipioCamera() as camera:
            frame_count = 0
            
            while True:
                # Capture and display frame
                data = camera.capture(show=True, save=False)
                frame_count += 1
                
                # Show frame counter
                if frame_count % 30 == 0:
                    print(f"Frame {frame_count}")
                
                # Handle keyboard input
                key = cv2.waitKey(30) & 0xFF
                
                if key == ord('q'):
                    print("Quitting live preview...")
                    break
                elif key == ord('s'):
                    print("Saving current frame...")
                    camera.capture(show=False, save=True)
                    print("Frame saved!")
                elif key == ord('i'):
                    camera.print_camera_info()
                    
    except Exception as e:
        print(f"Error in live preview: {e}")
    finally:
        cv2.destroyAllWindows()


def example_data_analysis():
    """Example 4: Analyzing captured data."""
    print("\n=== Example 4: Data Analysis ===")
    
    try:
        with PercipioCamera() as camera:
            # Capture data
            data = camera.capture(show=False, save=False)
            
            # Analyze color image
            if data.color_image is not None:
                color_img = data.color_image
                print(f"Color image analysis:")
                print(f"  Shape: {color_img.shape}")
                print(f"  Data type: {color_img.dtype}")
                print(f"  Min/Max values: {color_img.min()}/{color_img.max()}")
                print(f"  Mean RGB: {color_img.mean(axis=(0,1))}")
            
            # Analyze depth image
            if data.depth_image is not None:
                depth_img = data.depth_image
                print(f"\nDepth image analysis:")
                print(f"  Shape: {depth_img.shape}")
                print(f"  Data type: {depth_img.dtype}")
                print(f"  Min/Max values: {depth_img.min()}/{depth_img.max()}")
                print(f"  Mean depth: {depth_img.mean()}")
            
            # Analyze point cloud
            if data.point_cloud is not None:
                pc = data.point_cloud
                print(f"\nPoint cloud analysis:")
                print(f"  Shape: {pc.shape}")
                print(f"  Data type: {pc.dtype}")
                if pc.size > 0:
                    print(f"  X range: {pc[..., 0].min():.3f} to {pc[..., 0].max():.3f}")
                    print(f"  Y range: {pc[..., 1].min():.3f} to {pc[..., 1].max():.3f}")
                    print(f"  Z range: {pc[..., 2].min():.3f} to {pc[..., 2].max():.3f}")
                    
    except Exception as e:
        print(f"Error in data analysis: {e}")


def example_save_data():
    """Example 5: Saving captured data."""
    print("\n=== Example 5: Saving Data ===")
    
    try:
        with PercipioCamera() as camera:
            print("Capturing and saving data...")
            
            # Capture and save data
            data = camera.capture(show=False, save=True)
            
            print(f"Data saved with timestamp: {data.timestamp}")
            print("Check the 'capture/' directory for saved files:")
            print("  - color_<timestamp>.png")
            print("  - depth_<timestamp>.png")
            print("  - aligned_depth_<timestamp>.png")
            print("  - pointcloud_<timestamp>.npy")
            
    except Exception as e:
        print(f"Error saving data: {e}")


def example_custom_capture():
    """Example 6: Custom capture settings."""
    print("\n=== Example 6: Custom Capture Settings ===")
    
    try:
        # Initialize camera with custom settings
        with PercipioCamera(device_index=0, color_format_index=1, depth_format_index=0) as camera:
            print("Camera initialized with custom format indices")
            
            # Capture with custom parameters
            data = camera.capture(
                show=False,
                save=False,
                timeout_ms=5000,  # 5 second timeout
                align_depth_to_color=False  # Don't align depth to color
            )
            
            print("Custom capture completed")
            print(f"Aligned depth available: {data.aligned_depth_image is not None}")
            
    except Exception as e:
        print(f"Error in custom capture: {e}")


def main():
    """Run all examples."""
    print("Percipio Camera Wrapper Examples")
    print("=" * 40)
    
    examples = [
        ("Basic Capture", example_basic_capture),
        ("Camera Information", example_camera_info),
        ("Data Analysis", example_data_analysis),
        ("Save Data", example_save_data),
        ("Custom Capture", example_custom_capture),
        ("Live Preview", example_live_preview),  # Interactive example last
    ]
    
    for name, example_func in examples:
        try:
            example_func()
            input(f"\nPress Enter to continue to next example...")
        except KeyboardInterrupt:
            print(f"\nExample '{name}' interrupted by user")
            break
        except Exception as e:
            print(f"Error in example '{name}': {e}")
            input("Press Enter to continue...")
    
    print("\nAll examples completed!")


if __name__ == "__main__":
    main()
