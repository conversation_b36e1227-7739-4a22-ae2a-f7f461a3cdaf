# Percipio Camera Wrapper

A high-level, object-oriented Python interface for Percipio depth cameras, providing simplified access to camera functionality with context manager support.

## Features

- **Context Manager Support**: Automatic resource management with `__enter__` and `__exit__` methods
- **Image Acquisition**: Capture color images, depth images, and point clouds
- **Image Alignment**: Automatic depth-to-color alignment
- **Camera Parameters**: Easy access to intrinsic/extrinsic parameters and distortion coefficients
- **Visualization**: Built-in display functionality using OpenCV
- **Data Saving**: Automatic saving of captured data to files
- **Error Handling**: Robust error handling and device disconnection detection

## Installation

1. Ensure you have the Percipio SDK installed in the `pcammls_py39/` directory
2. Install required Python dependencies:
   ```bash
   pip install opencv-python numpy
   ```

## Quick Start

### Basic Usage

```python
from pcam import PercipioCamera

# Simple capture with context manager
with PercipioCamera() as camera:
    # Capture a single frame
    data = camera.capture(show=True, save=False)
    
    if data.color_image is not None:
        print(f"Color image shape: {data.color_image.shape}")
    if data.depth_image is not None:
        print(f"Depth image shape: {data.depth_image.shape}")
    if data.point_cloud is not None:
        print(f"Point cloud shape: {data.point_cloud.shape}")
```

### Live Capture Loop

```python
import cv2
from pcam import PercipioCamera

with PercipioCamera() as camera:
    while True:
        # Capture and display frames
        data = camera.capture(show=True)
        
        # Exit on 'q' key
        if cv2.waitKey(30) & 0xFF == ord('q'):
            break

cv2.destroyAllWindows()
```

### Accessing Camera Information

```python
with PercipioCamera() as camera:
    # Print all camera information
    camera.print_camera_info()
    
    # Get specific parameters
    resolutions = camera.get_resolution()
    intrinsics = camera.get_intrinsics()
    extrinsics = camera.get_extrinsics()
    distortion = camera.get_distortion_coefficients()
    
    print(f"Color resolution: {resolutions.get('color')}")
    print(f"Depth resolution: {resolutions.get('depth')}")
```

### Saving Data

```python
with PercipioCamera() as camera:
    # Capture and save data automatically
    data = camera.capture(show=False, save=True)
    
    # Data will be saved to 'capture/' directory with timestamps
    # - color_<timestamp>.png
    # - depth_<timestamp>.png
    # - aligned_depth_<timestamp>.png
    # - pointcloud_<timestamp>.npy
```

## API Reference

### PercipioCamera Class

#### Constructor
```python
PercipioCamera(device_index=0, color_format_index=2, depth_format_index=0)
```

- `device_index`: Index of the camera device to use (default: 0)
- `color_format_index`: Index of color format to use (default: 2)
- `depth_format_index`: Index of depth format to use (default: 0)

#### Methods

##### capture()
```python
capture(show=False, save=False, timeout_ms=2000, align_depth_to_color=True) -> CaptureData
```

Capture images and point cloud data.

- `show`: Display images using OpenCV
- `save`: Save captured data to files
- `timeout_ms`: Timeout for frame capture in milliseconds
- `align_depth_to_color`: Align depth image to color coordinate system

Returns a `CaptureData` object containing:
- `color_image`: RGB color image (numpy array)
- `depth_image`: Depth image for visualization (numpy array)
- `aligned_depth_image`: Depth image aligned to color coordinate (numpy array)
- `point_cloud`: 3D point cloud data (numpy array)
- `timestamp`: Capture timestamp

##### Camera Information Methods
- `get_resolution()`: Get camera resolutions
- `get_intrinsics()`: Get intrinsic parameters
- `get_extrinsics()`: Get extrinsic parameters
- `get_distortion_coefficients()`: Get distortion coefficients
- `print_camera_info()`: Print detailed camera information

##### Stream Control
- `start_streams()`: Start camera streams (called automatically)
- `stop_streams()`: Stop camera streams
- `is_connected()`: Check if camera is still connected

## Examples

### Example 1: Camera Information
```python
from pcam import PercipioCamera

with PercipioCamera() as camera:
    camera.print_camera_info()
```

### Example 2: Single Frame Capture
```python
from pcam import PercipioCamera

with PercipioCamera() as camera:
    data = camera.capture()
    print(f"Captured at: {data.timestamp}")
```

### Example 3: Live Preview
```python
import cv2
from pcam import PercipioCamera

with PercipioCamera() as camera:
    while True:
        camera.capture(show=True)
        if cv2.waitKey(1) & 0xFF == ord('q'):
            break
cv2.destroyAllWindows()
```

## Testing

Run the test suite to verify functionality:

```bash
python test_pcam.py
```

The test suite includes:
- Camera information retrieval
- Single frame capture
- Data saving functionality
- Live capture with user interaction

## Error Handling

The wrapper includes comprehensive error handling:

- Automatic device detection and connection
- Graceful handling of device disconnection
- Timeout handling for frame capture
- Resource cleanup on exit

## Dependencies

- Python 3.6+
- OpenCV (cv2)
- NumPy
- Percipio SDK (pcammls_py39)

## Notes

- The wrapper automatically handles camera initialization and cleanup
- Use the context manager (`with` statement) for proper resource management
- The SDK files must be present in the `pcammls_py39/` directory
- Point cloud data is generated from depth images using camera calibration
- Aligned depth images are registered to the color camera coordinate system
