# 12-Point 2D Calibration System - Usage Guide

## Overview
This system performs a 12-point calibration between a UR robot and HK camera for 2D calibration purposes. It generates a 3x3 pose grid around a reference position and adds two rotational poses.

## Quick Start

### 1. Configuration
Update the configuration parameters in the script:
- `ROBOT_IP`: Set to your robot's IP address (e.g., "*************")
- `REFERENCE_POSE`: Adjust based on your setup
  - Format: [x, y, z, rx, ry, rz] in UR base frame
  - Units: meters for position, radians for rotation
- `GRID_STEP`: Grid spacing (default 0.01m = 1cm)

### 2. Run Calibration
```bash
cd /home/<USER>/workspace/develop/seatbelt/camera/application
python calibration_2d_12_point.py
```

### 3. Outputs
- **Images**: 12 calibration images saved as `calib_image_001.jpg` to `calib_image_012.jpg`
- **Pose Data**: `pose.txt` with 12 lines of poses in format `x,y,z,rx,ry,rz`

## Calibration Sequence

The system generates 12 poses in this order:

1. **Grid Positions** (9 poses): 3x3 grid around reference pose
   - Y offsets: [-0.01, 0, 0.01] meters
   - Z offsets: [-0.01, 0, 0.01] meters
   - Order: bottom-left to top-right (row by row)

2. **Rotation Poses** (2 poses):
   - Pose 11: +25° rotation around Z-axis (rz + 0.4363 rad)
   - Pose 12: -25° rotation around Z-axis (rz - 0.4363 rad)

## File Structure
```
camera/application/
├── calibration_2d_12_point.py    # Main calibration script
├── calibration_images/            # Generated images
│   ├── calib_image_001.jpg        # Center position
│   ├── calib_image_002.jpg        # Center + Y offset
│   ├── ...                        # All 12 images
├── pose.txt                       # 12 poses data
```

## Safety Notes
- Ensure robot workspace is clear before starting
- Verify reference pose is within robot reach
- Check that camera has clear view of calibration target
- Monitor system during operation

## Troubleshooting

### Connection Issues
- **Robot**: Check IP address and network connectivity
- **Camera**: Verify camera index and USB connection

### Movement Issues
- Adjust `velocity` and `acceleration` parameters if needed
- Ensure robot is in remote control mode

### Image Capture Issues
- Check camera focus and lighting conditions
- Verify target is visible in camera frame

## Customization

### Grid Parameters
```python
# Modify these in the script
grid_step = 0.02  # 2cm instead of 1cm
# Or use custom grid offsets
```

### Movement Speed
```python
# Adjust movement parameters
velocity = 0.05  # m/s (slower)
acceleration = 0.3  # m/s² (gentler)
```

## Coordinate System
- All poses are in UR robot base coordinate system
- Units: meters (position), radians (rotation)
- Axis-angle representation for rotations