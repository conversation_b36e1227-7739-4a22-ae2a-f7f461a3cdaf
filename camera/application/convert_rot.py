#!/usr/bin/env python3
"""
Rotation conversion utilities for axis-angle and RPY representations.

This module provides conversion functions between axis-angle representation (rx, ry, rz)
and RPY (Roll-Pitch-Yaw) angles, using SciPy's Rotation class for
high accuracy and efficiency in robotics applications.

Author: Jack
Date: 2025-07-25
"""

import numpy as np
from typing import Tuple, Union
import math
from scipy.spatial.transform import Rotation as R


def axis_angle_to_rpy(rx: float, ry: float, rz: float) -> Tuple[float, float, float]:
    """
    Convert axis-angle representation to RPY (Roll-Pitch-Yaw) angles.
    
    The axis-angle representation defines a rotation by a vector [rx, ry, rz]
    where the direction is the rotation axis and the magnitude is the rotation angle.
    
    Args:
        rx (float): X component of rotation axis vector
        ry (float): Y component of rotation axis vector
        rz (float): Z component of rotation axis vector
    
    Returns:
        Tuple[float, float, float]: (roll, pitch, yaw) angles in radians
        
    Raises:
        ValueError: If the input vector has zero magnitude
        
    Notes:
        Uses SciPy's Rotation.from_rotvec() for axis-angle to rotation matrix conversion
        and Rotation.as_euler() for RPY angles extraction.
    """
    # Handle zero rotation case
    if abs(rx) < 1e-10 and abs(ry) < 1e-10 and abs(rz) < 1e-10:
        return (0.0, 0.0, 0.0)
    
    # Create rotation from rotation vector
    rot_vec = np.array([rx, ry, rz])
    try:
        rotation = R.from_rotvec(rot_vec)
        # Extract RPY angles (XYZ convention)
        rpy = rotation.as_euler('xyz')
        return (rpy[0], rpy[1], rpy[2])
    except Exception as e:
        raise ValueError(f"Invalid rotation vector: {e}")


def rpy_to_axis_angle(roll: float, pitch: float, yaw: float) -> Tuple[float, float, float]:
    """
    Convert RPY (Roll-Pitch-Yaw) angles to axis-angle representation.
    
    Args:
        roll (float): Roll angle in radians (rotation around X-axis)
        pitch (float): Pitch angle in radians (rotation around Y-axis)
        yaw (float): Yaw angle in radians (rotation around Z-axis)
    
    Returns:
        Tuple[float, float, float]: (rx, ry, rz) axis-angle vector
        
    Notes:
        Uses SciPy's Rotation.from_euler() for RPY to rotation matrix conversion
        and Rotation.as_rotvec() for axis-angle extraction.
    """
    # Create rotation from RPY angles
    try:
        rotation = R.from_euler('xyz', [roll, pitch, yaw])
        # Extract rotation vector (axis-angle representation)
        rot_vec = rotation.as_rotvec()
        return (rot_vec[0], rot_vec[1], rot_vec[2])
    except Exception as e:
        raise ValueError(f"Invalid RPY angles: {e}")


def rotation_matrix_to_rpy(R_matrix: np.ndarray) -> Tuple[float, float, float]:
    """
    Extract RPY angles from a rotation matrix.
    
    Args:
        R_matrix (np.ndarray): 3x3 rotation matrix
        
    Returns:
        Tuple[float, float, float]: (roll, pitch, yaw) angles in radians
        
    Raises:
        ValueError: If input is not a valid 3x3 rotation matrix
    """
    if R_matrix.shape != (3, 3):
        raise ValueError("Input must be a 3x3 matrix")
    
    # Check if it's a valid rotation matrix
    if not np.allclose(R_matrix @ R_matrix.T, np.eye(3), atol=1e-6):
        raise ValueError("Input must be a valid rotation matrix")
    
    try:
        # Create rotation from rotation matrix
        rotation = R.from_matrix(R_matrix)
        # Extract RPY angles (XYZ convention)
        rpy = rotation.as_euler('xyz')
        return (rpy[0], rpy[1], rpy[2])
    except Exception as e:
        raise ValueError(f"Invalid rotation matrix: {e}")


def rpy_to_rotation_matrix(roll: float, pitch: float, yaw: float) -> np.ndarray:
    """
    Construct rotation matrix from RPY angles.
    
    Args:
        roll (float): Roll angle in radians
        pitch (float): Pitch angle in radians
        yaw (float): Yaw angle in radians
    
    Returns:
        np.ndarray: 3x3 rotation matrix
    """
    try:
        # Create rotation from RPY angles
        rotation = R.from_euler('xyz', [roll, pitch, yaw])
        # Get rotation matrix
        return rotation.as_matrix()
    except Exception as e:
        raise ValueError(f"Invalid RPY angles: {e}")


def _run_tests():
    """Run simple test cases to verify the conversion functions."""
    print("Running rotation conversion tests...")
    
    # Test case 1: Zero rotation
    print("\nTest 1: Zero rotation")
    rx, ry, rz = 0.0, 0.0, 0.0
    roll, pitch, yaw = axis_angle_to_rpy(rx, ry, rz)
    print(f"Axis-angle ({rx}, {ry}, {rz}) -> RPY ({roll:.6f}, {pitch:.6f}, {yaw:.6f})")
    
    rx_back, ry_back, rz_back = rpy_to_axis_angle(roll, pitch, yaw)
    print(f"RPY ({roll:.6f}, {pitch:.6f}, {yaw:.6f}) -> Axis-angle ({rx_back:.6f}, {ry_back:.6f}, {rz_back:.6f})")
    
    # Test case 2: 90-degree rotation around X-axis
    print("\nTest 2: 90-degree rotation around X-axis")
    angle = math.pi / 2
    rx, ry, rz = angle, 0.0, 0.0
    roll, pitch, yaw = axis_angle_to_rpy(rx, ry, rz)
    print(f"Axis-angle ({rx:.6f}, {ry:.6f}, {rz:.6f}) -> RPY ({roll:.6f}, {pitch:.6f}, {yaw:.6f})")
    
    rx_back, ry_back, rz_back = rpy_to_axis_angle(roll, pitch, yaw)
    print(f"RPY ({roll:.6f}, {pitch:.6f}, {yaw:.6f}) -> Axis-angle ({rx_back:.6f}, {ry_back:.6f}, {rz_back:.6f})")
    
    # Test case 3: 45-degree rotation around Z-axis
    print("\nTest 3: 45-degree rotation around Z-axis")
    angle = math.pi / 4
    rx, ry, rz = 0.0, 0.0, angle
    roll, pitch, yaw = axis_angle_to_rpy(rx, ry, rz)
    print(f"Axis-angle ({rx:.6f}, {ry:.6f}, {rz:.6f}) -> RPY ({roll:.6f}, {pitch:.6f}, {yaw:.6f})")
    
    rx_back, ry_back, rz_back = rpy_to_axis_angle(roll, pitch, yaw)
    print(f"RPY ({roll:.6f}, {pitch:.6f}, {yaw:.6f}) -> Axis-angle ({rx_back:.6f}, {ry_back:.6f}, {rz_back:.6f})")
    
    # Test case 4: Arbitrary rotation
    print("\nTest 4: Arbitrary rotation")
    rx, ry, rz = 0.5, 0.3, 0.8
    roll, pitch, yaw = axis_angle_to_rpy(rx, ry, rz)
    print(f"Axis-angle ({rx:.6f}, {ry:.6f}, {rz:.6f}) -> RPY ({roll:.6f}, {pitch:.6f}, {yaw:.6f})")
    
    rx_back, ry_back, rz_back = rpy_to_axis_angle(roll, pitch, yaw)
    print(f"RPY ({roll:.6f}, {pitch:.6f}, {yaw:.6f}) -> Axis-angle ({rx_back:.6f}, {ry_back:.6f}, {rz_back:.6f})")
    
    # Verify round-trip accuracy
    print("\nRound-trip accuracy check:")
    original = np.array([0.5, 0.3, 0.8])
    rpy = axis_angle_to_rpy(*original)
    recovered = rpy_to_axis_angle(*rpy)
    error = np.linalg.norm(original - np.array(recovered))
    print(f"Original: {original}")
    print(f"Recovered: {np.array(recovered)}")
    print(f"Error: {error:.2e}")
    
    # Test rotation matrix functions
    print("\nTesting rotation matrix functions...")
    # Create rotation matrix from RPY
    R_mat = rpy_to_rotation_matrix(0.5, 0.3, 0.8)
    print(f"RPY (0.5, 0.3, 0.8) -> Rotation matrix:")
    print(R_mat)
    
    # Extract RPY from rotation matrix
    rpy_from_mat = rotation_matrix_to_rpy(R_mat)
    print(f"Rotation matrix -> RPY: {rpy_from_mat}")
    
    # Verify round-trip for matrix functions
    R_mat_again = rpy_to_rotation_matrix(*rpy_from_mat)
    error_mat = np.linalg.norm(R_mat - R_mat_again)
    print(f"Matrix round-trip error: {error_mat:.2e}")
    
    print("\nAll tests completed.")


if __name__ == "__main__":
    _run_tests()