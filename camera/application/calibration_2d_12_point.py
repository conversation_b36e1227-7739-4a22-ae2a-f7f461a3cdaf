#!/usr/bin/env python3
"""
UR Robot and HK Camera 12-Point 2D Calibration System

This script implements a 12-point calibration system for UR robots and HK cameras.
It generates a 3x3 pose grid around a reference pose, captures images at each position,
and records the corresponding robot poses for calibration purposes.

Features:
- 3x3 pose grid generation with Y/Z translations
- Additional rotation poses at ±25°
- Image capture and saving
- Pose data recording in UR base coordinate system
- Error handling and progress logging
"""

import os
import sys
import time
import logging
import numpy as np
from typing import List, Tuple
from pathlib import Path

# Add parent directories to path for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'robo_ctl'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'camera', 'HKcam'))

from robo_ctl.UR.ur_robot_controller import URRobotController
from camera.HKcam.hk_camera_manager import HKCameraManager
from camera.application.convert_rot import axis_angle_to_rpy, rpy_to_axis_angle


class Calibration2D12Point:
    """12-point 2D calibration system for UR robot and HK camera."""
    
    def __init__(self, robot_ip: str, camera_index: int = 0, grid_step: float = 0.01):
        """
        Initialize calibration system.
        
        Args:
            robot_ip: IP address of the UR robot
            camera_index: Camera index for HK camera
            grid_step: Grid spacing in meters for Y/Z translations
        """
        self.robot_ip = robot_ip
        self.camera_index = camera_index
        self.grid_step = grid_step
        
        # Initialize logger
        self.logger = self._setup_logger()
        
        # Initialize robot and camera controllers
        self.robot = None
        self.camera = None
        
        # Calibration parameters
        self.reference_pose = None
        self.calibration_poses = []
        self.pose_data = []
        
        # Output directories
        self.output_dir = Path('/home/<USER>/workspace/develop/seatbelt/camera/application/calibration_images')
        self.pose_file = self.output_dir.parent / 'pose.txt'
        
    def _setup_logger(self) -> logging.Logger:
        """Setup logging configuration."""
        logger = logging.getLogger('Calibration2D12Point')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            
        return logger
        
    def initialize_system(self) -> bool:
        """
        Initialize robot and camera connections.
        
        Returns:
            True if both connections successful, False otherwise
        """
        try:
            # Initialize robot
            self.logger.info(f"Connecting to robot at {self.robot_ip}...")
            self.robot = URRobotController(self.robot_ip)
            
            if not self.robot.is_connected():
                self.logger.error("Failed to connect to robot")
                return False
                
            self.logger.info("Robot connected successfully")
            
            # Initialize camera
            self.logger.info(f"Connecting to camera at index {self.camera_index}...")
            self.camera = HKCameraManager(index=self.camera_index)
            
            if not self.camera.connect():
                self.logger.error("Failed to connect to camera")
                return False
                
            if not self.camera.start_capture():
                self.logger.error("Failed to start camera capture")
                return False
                
            self.logger.info("Camera connected and capture started successfully")
            
            # Create output directory
            self.output_dir.mkdir(parents=True, exist_ok=True)
            self.logger.info(f"Output directory created: {self.output_dir}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"System initialization failed: {e}")
            return False
            
    def set_reference_pose(self, pose: List[float]) -> None:
        """
        Set the reference pose for calibration grid generation.
        
        Args:
            pose: Reference pose [x, y, z, rx, ry, rz] in UR base frame
        """
        if len(pose) != 6:
            raise ValueError("Pose must contain 6 elements [x, y, z, rx, ry, rz]")
            
        self.reference_pose = pose.copy()
        self.logger.info(f"Reference pose set: {pose}")
        
    def generate_calibration_poses(self) -> List[List[float]]:
        """
        Generate 12 calibration poses based on reference pose.
        
        Returns:
            List of 12 poses, each pose is [x, y, z, rx, ry, rz]
        """
        if self.reference_pose is None:
            raise ValueError("Reference pose not set. Call set_reference_pose() first.")
            
        poses = []
        x_ref, y_ref, z_ref, rx_ref, ry_ref, rz_ref = self.reference_pose
        
        # 3x3 grid generation (9 positions)
        self.logger.info("Generating 3x3 grid poses...")
        
        # Y and Z offsets for 3x3 grid
        y_offsets = [-self.grid_step, 0, self.grid_step]
        z_offsets = [-self.grid_step, 0, self.grid_step]
        
        for y_offset in y_offsets:
            for z_offset in z_offsets:
                pose = [
                    x_ref,
                    y_ref + y_offset,
                    z_ref + z_offset,
                    rx_ref,
                    ry_ref,
                    rz_ref
                ]
                poses.append(pose)
                
        self.logger.info(f"Generated {len(poses)} grid poses")
        
        # Add rotation poses (±25° around calibration plane normal)
        self.logger.info("Adding rotation poses...")
        
        # Convert reference pose axis-angle to RPY angles
        roll_ref, pitch_ref, yaw_ref = axis_angle_to_rpy(rx_ref, ry_ref, rz_ref)
        
        # +25° rotation around yaw (Z-axis)
        rotation_positive = 25 * np.pi / 180  # Convert to radians
        roll_pos = roll_ref + rotation_positive
        
        # Convert back to axis-angle representation
        rx_pos, ry_pos, rz_pos = rpy_to_axis_angle(roll_pos, pitch_ref, yaw_ref)
        pose_pos = [
            x_ref,
            y_ref,
            z_ref,
            rx_pos,
            ry_pos,
            rz_pos
        ]
        poses.append(pose_pos)
        
        # -25° rotation around yaw (Z-axis)
        rotation_negative = -25 * np.pi / 180  # Convert to radians
        roll_neg = roll_ref + rotation_negative
                
        # Convert back to axis-angle representation
        rx_neg, ry_neg, rz_neg = rpy_to_axis_angle(roll_neg, pitch_ref, yaw_ref)
        pose_neg = [
            x_ref,
            y_ref,
            z_ref,
            rx_neg,
            ry_neg,
            rz_neg
        ]
        poses.append(pose_neg)
        
        self.calibration_poses = poses
        self.logger.info(f"Total calibration poses generated: {len(poses)}")
        
        return poses
        
    def move_to_pose(self, pose: List[float], velocity: float = 0.1, acceleration: float = 0.5) -> bool:
        """
        Move robot to specified pose.
        
        Args:
            pose: Target pose [x, y, z, rx, ry, rz]
            velocity: Movement velocity in m/s
            acceleration: Movement acceleration in m/s²
            
        Returns:
            True if movement successful, False otherwise
        """
        try:
            self.logger.info(f"Moving to pose: {pose}")
            success = self.robot.move_linear(pose, velocity, acceleration)
            
            if success:
                # Wait a moment for robot to stabilize
                time.sleep(0.5)
                
                # Verify current pose
                current_pose = self.robot.get_tcp_pose()
                if current_pose:
                    self.logger.info(f"Current pose: {current_pose}")
                    return True
                else:
                    self.logger.warning("Could not verify current pose")
                    return False
            else:
                self.logger.error("Failed to move to pose")
                return False
                
        except Exception as e:
            self.logger.error(f"Error during movement: {e}")
            return False
            
    def capture_image(self, pose_index: int) -> bool:
        """
        Capture and save image at current pose.
        
        Args:
            pose_index: Index of the current pose (1-12)
            
        Returns:
            True if image captured successfully, False otherwise
        """
        try:
            # Generate filename
            filename = f"calib_image_{pose_index:03d}.jpg"
            filepath = self.output_dir / filename
            
            # Capture image
            self.logger.info(f"Capturing image: {filename}")
            image = self.camera.capture_single_image(str(filepath))
            
            if image is not None:
                self.logger.info(f"Image saved: {filepath}")
                return True
            else:
                self.logger.error("Failed to capture image")
                return False
                
        except Exception as e:
            self.logger.error(f"Error during image capture: {e}")
            return False
            
    def record_pose(self, pose: List[float]) -> None:
        """
        Record robot pose to data list.
        
        Args:
            pose: Pose to record [x, y, z, rx, ry, rz]
        """
        self.pose_data.append(pose)
        
    def save_pose_data(self) -> bool:
        """
        Save pose data to file.
        
        Returns:
            True if file saved successfully, False otherwise
        """
        try:
            with open(self.pose_file, 'w') as f:
                for pose in self.pose_data:
                    pose_str = ','.join(map(str, pose))
                    f.write(f"{pose_str}\n")
                    
            self.logger.info(f"Pose data saved to: {self.pose_file}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error saving pose data: {e}")
            return False
            
    def run_calibration(self, reference_pose: List[float]) -> bool:
        """
        Run complete 12-point calibration sequence.
        
        Args:
            reference_pose: Reference pose for calibration grid
            
        Returns:
            True if calibration completed successfully, False otherwise
        """
        try:
            self.logger.info("Starting 12-point calibration sequence...")
            
            # Set reference pose
            self.set_reference_pose(reference_pose)
            
            # Generate calibration poses
            poses = self.generate_calibration_poses()
            
            # Initialize data collection
            self.pose_data = []
            
            # Move through each pose and capture image
            for i, pose in enumerate(poses, 1):
                self.logger.info(f"Processing pose {i}/12")
                
                # Move to pose
                if not self.move_to_pose(pose):
                    self.logger.error(f"Failed to move to pose {i}")
                    return False
                
                # Record pose
                current_pose = self.robot.get_tcp_pose()
                if current_pose:
                    self.record_pose(current_pose)
                else:
                    self.record_pose(pose)  # Use generated pose if can't read current
                    
                # Capture image
                if not self.capture_image(i):
                    self.logger.error(f"Failed to capture image at pose {i}")
                    return False
                    
                # Small delay between poses
                time.sleep(0.2)
                
            # Save pose data
            if not self.save_pose_data():
                self.logger.error("Failed to save pose data")
                return False
                
            self.logger.info("12-point calibration completed successfully!")
            self.logger.info(f"Images saved to: {self.output_dir}")
            self.logger.info(f"Pose data saved to: {self.pose_file}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Calibration failed: {e}")
            return False
            
    def cleanup(self) -> None:
        """Clean up resources and disconnect."""
        if self.robot:
            self.robot.disconnect()
            self.logger.info("Robot disconnected")
            
        if self.camera:
            self.camera.disconnect()
            self.logger.info("Camera disconnected")


def main():
    """Main execution function."""
    # Configuration
    ROBOT_IP = "*************"  # Update with actual robot IP
    CAMERA_INDEX = 0
    GRID_STEP = 0.01  # 1cm grid spacing
    
    # Reference pose (adjust based on your setup)
    # Format: [x, y, z, rx, ry, rz] in UR base frame
    REFERENCE_POSE = [0.4, 0.1, 0.2, 0, 0, 0]  # Update as needed
    
    # Initialize calibration system
    calibrator = Calibration2D12Point(
        robot_ip=ROBOT_IP,
        camera_index=CAMERA_INDEX,
        grid_step=GRID_STEP
    )
    
    try:
        # Initialize system
        if not calibrator.initialize_system():
            calibrator.logger.error("Failed to initialize system")
            return 1
            
        # Run calibration
        if calibrator.run_calibration(REFERENCE_POSE):
            calibrator.logger.info("Calibration completed successfully")
            return 0
        else:
            calibrator.logger.error("Calibration failed")
            return 1
            
    except KeyboardInterrupt:
        calibrator.logger.info("Calibration interrupted by user")
        return 1
        
    except Exception as e:
        calibrator.logger.error(f"Unexpected error: {e}")
        return 1
        
    finally:
        calibrator.cleanup()


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)