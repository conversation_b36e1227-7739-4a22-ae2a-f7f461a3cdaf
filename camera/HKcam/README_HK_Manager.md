# HK Camera Manager

基于海康威视相机功能封装的相机管理类。

## 功能特性

- 🔗 **相机连接管理**：支持索引和序列号两种连接方式
- 📸 **图像采集**：实时图像读取和单张图像捕获
- 🖥️ **实时显示**：基于OpenCV的实时图像显示
- 💾 **图像保存**：支持图像保存到指定路径
- 🛠️ **资源管理**：自动资源清理和上下文管理器支持
- ⚡ **错误处理**：完善的异常处理和状态管理

## 文件结构

```
camera/HKcam/
├── hk_camera_manager.py   # 封装的相机管理类
├── example_usage.py       # 使用示例
└── README.md             # 本文件
```

## 依赖要求

```python
import time
import cv2
from pyhkcam import information
from pyhkcam.pyhkcam import *
```

确保已安装：
- `pyhkcam` - 海康威视相机Python SDK
- `opencv-python` - 图像处理和显示

## 快速开始

### 基本使用

```python
from hk_camera_manager import HKCameraManager

# 创建相机管理器实例
camera = HKCameraManager(index=0)

# 连接并启动相机
if camera.connect():
    if camera.start_capture():
        # 实时显示
        camera.capture_and_display()

# 清理资源
camera.disconnect()
```

### 使用上下文管理器

```python
# 自动资源管理
with HKCameraManager(index=0) as camera:
    # 捕获单张图像
    img = camera.capture_single_image("captured.png")
    if img is not None:
        print(f"图像已捕获，尺寸: {img.shape}")
```

### 使用序列号连接

```python
# 使用序列号连接
camera = HKCameraManager(serial_number='DSU004600000383')
if camera.connect():
    camera.start_capture()
    # ... 其他操作
```

## API 参考

### 类初始化

```python
HKCameraManager(index=0, serial_number=None)
```

**参数：**
- `index` (int): 相机索引，默认为 0
- `serial_number` (str): 相机序列号，可选

### 主要方法

#### `enumerate_devices()`
枚举可用的相机设备
```python
device_info = camera.enumerate_devices()
```

#### `connect()`
连接到相机
```python
success = camera.connect()  # 返回 True/False
```

#### `start_capture()`
启动图像采集
```python
success = camera.start_capture()  # 返回 True/False
```

#### `read_image(timeout_ms=1000)`
读取单帧图像
```python
img = camera.read_image(timeout_ms=1000)  # 返回 numpy.ndarray 或 None
```

#### `capture_and_display(window_name='HK Camera', wait_key=30)`
实时显示图像流
```python
camera.capture_and_display()  # 按 'q' 退出
```

#### `capture_single_image(save_path=None, timeout_ms=1000)`
捕获单张图像
```python
img = camera.capture_single_image("image.png")  # 可选保存路径
```

#### `disconnect()`
断开连接并清理资源
```python
camera.disconnect()
```

## 使用示例

运行提供的示例文件：

```bash
python example_usage.py
```

示例包括：
2. **上下文管理器** - 自动资源管理
3. **单张图像捕获** - 捕获并保存图像
4. **序列号连接** - 使用设备序列号
5. **自定义显示设置** - 自定义窗口和刷新率


## 注意事项

2. **触发模式**：`set_trigger_mode()` 和 `soft_trigger()` 方法已提供但未在原始代码中测试
3. **资源清理**：推荐使用上下文管理器或手动调用 `disconnect()`
4. **错误处理**：所有方法都包含异常处理和状态检查

## 扩展功能

可以进一步扩展：
- 与机器人控制系统集成
- 图像处理和分析
- 批量图像采集
- 自定义触发模式

## 故障排除

1. **相机连接失败**：检查相机是否正确连接，驱动是否安装
2. **图像读取超时**：调整 `timeout_ms` 参数
3. **显示窗口问题**：确保安装了支持GUI的OpenCV版本

## 版本信息

- 兼容 `pyhkcam` 库
- 支持 Python 3.x 