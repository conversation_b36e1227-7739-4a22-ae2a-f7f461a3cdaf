import PIL.Image
import numpy as np
import matplotlib.pyplot as plt
import cv2
import open3d as o3d
import torch
import copy
import time
import sys

from pathlib import Path
# Add preprocess directory to Python path so that `import models` resolves correctly
sys.path.append(str(Path(__file__).resolve().parent / 'preprocess'))
print(sys.path)

from preprocess.sam_predictor import Predictor
from preprocess.infer_wrap import YOLOv8TRTDetector
from preprocess.mask2cloud import mask_to_point_cloud
from utils import bbox2points, draw_bbox, subplot_notick, pose_6d_to_matrix, matrix_to_pose_6d, matrix_to_axis_6d

from pose_estimator import PoseEstimator
from robo_ctl.ur_robot_controller import URRobotController

# Import RVC camera wrapper
sys.path.append(str(Path(__file__).resolve().parent / 'camera' / 'RVC'))
from rvc_camera_wrapper import RVCCameraWrapper, CameraType, <PERSON><PERSON>

def get_user_confirmation():
    """Get user confirmation to move the robot."""
    user_input = input("Move the robot? (y/n): ").lower()
    if user_input == 'n':
        print("Operation cancelled.")
        exit()
    elif user_input != 'y':
        print("Invalid input!")
        exit()
    print("Robot moving!")


def capture_rvc_data(device_sn=None, exposure_time=100):
    """
    Capture image and depth data from RVC camera. 

    Args:
        device_sn: Device serial number (None for first available)
        exposure_time: Camera exposure time

    Returns:
        tuple: (rgb_image, depth_image, camera_intrinsic) or (None, None, None) if failed
    """
    try:
        print("Initializing RVC camera...")
        with RVCCameraWrapper(CameraType.X2, device_sn=device_sn, camera_id=CameraID.LEFT) as camera:
            # Set exposure time
            camera.set_exposure_time(exposure_time)

            # Get device info
            device_info = camera.get_device_info()
            print(f"Using camera: {device_info.get('name', 'Unknown')} - {device_info.get('serial_number', 'Unknown')}")

            # Capture frame
            print("Capturing frame...")
            if not camera.capture_single():
                print("Failed to capture frame")
                return None, None, None

            # Get image (may be grayscale)
            grayscale_image = camera.get_image()
            if grayscale_image is None:
                print("Failed to get image")
                return None, None, None

            # Handle possible formats and convert to RGB
            if len(grayscale_image.shape) == 3 and grayscale_image.shape[2] == 3:
                rgb_image = cv2.cvtColor(grayscale_image, cv2.COLOR_BGR2RGB)
            elif len(grayscale_image.shape) == 2:
                rgb_image = cv2.cvtColor(grayscale_image, cv2.COLOR_GRAY2RGB)
            else:
                print(f"Unexpected image format: {grayscale_image.shape}")
                return None, None, None

            # Get depth map
            depth_map_obj = camera.get_depth_map()
            if depth_map_obj is None:
                print("Failed to get depth map")
                return None, None, None

            # Convert depth map to numpy array
            # Note: RVC depth map is typically in meters, which is what we need
            # We need to save it temporarily and read it back to get the numpy array
            import tempfile
            import os

            with tempfile.NamedTemporaryFile(suffix='.tiff', delete=False) as temp_file:
                temp_path = temp_file.name

            try:
                # Save depth map to temporary file
                depth_map_obj.SaveDepthMap(temp_path, True)

                # Read depth map as numpy array
                depth_image = cv2.imread(temp_path, cv2.IMREAD_ANYDEPTH)

                if depth_image is None:
                    print("Failed to read depth map from temporary file")
                    return None, None, None

                # Ensure depth is in float32 format and in meters
                depth_image = depth_image.astype(np.float32)

            finally:
                # Clean up temporary file
                if os.path.exists(temp_path):
                    os.unlink(temp_path)

            # Get camera intrinsic parameters
            intrinsic_matrix, distortion = camera.get_camera_parameters()
            if intrinsic_matrix is None:
                print("Failed to get camera parameters, using default RVC left camera intrinsic")
                # Use the default RVC left camera intrinsic from the original code
                camera_intrinsic = np.array([
                    [1577.85595703, 0, 717.44384765],
                    [0, 1578.40002441, 565.813415527],
                    [0, 0, 1]
                ])
            else:
                camera_intrinsic = intrinsic_matrix

            print(f"Captured image shape: {grayscale_image.shape} (converted to RGB: {rgb_image.shape})")
            print(f"Captured depth image shape: {depth_image.shape}")
            print(f"Depth image dtype: {depth_image.dtype}")
            print(f"Depth range: {depth_image.min():.3f} - {depth_image.max():.3f} meters")
            print("Camera intrinsic matrix:")
            print(camera_intrinsic)

            return rgb_image, depth_image, camera_intrinsic

    except Exception as e:
        print(f"Error capturing RVC data: {e}")
        import traceback
        traceback.print_exc()
        return None, None, None


# Rename function and remove fallback parameters
def get_image_data(device_sn=None, exposure_time=100):
    """
    Capture RGB and depth data from RVC camera

    Args:
        device_sn: Device serial number (None for first available)
        exposure_time: Camera exposure time

    Returns:
        tuple: (rgb_image, depth_image, camera_intrinsic) or (None, None, None) if failed
    """
    # Try RVC camera first
    rgb_image, depth_image, camera_intrinsic = capture_rvc_data(device_sn, exposure_time)

    if rgb_image is not None and depth_image is not None:
        print("✓ Successfully captured data from RVC camera")
        return rgb_image, depth_image, camera_intrinsic

    # If capture failed, just return None
    return None, None, None


def main():
    # Path to template file
    template = 'data/templateA/templateA.ply'

    # RVC camera configuration
    device_sn = None  # Use None for first available camera, or specify SN like "M2GM012W019"
    exposure_time = 100  # Adjust exposure time as needed

    # config infer mask
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    yolo_engine = 'preprocess/weights/yolov8_seatbelt_v1.engine'
    image_encoder = 'preprocess/weights/resnet18_image_encoder_fp16.engine'
    mask_decoder = 'preprocess/weights/mobile_sam_mask_decoder_fp16.engine'

    # Initialize detector and sam predictor
    detector = YOLOv8TRTDetector(yolo_engine, device)
    sam_predictor = Predictor(device, image_encoder, mask_decoder)

    # Capture data from RVC camera with fallback
    print("=== Capturing data from RVC camera ===")
    rgb_image, depth_image, camera_intrinsic = get_image_data(
        device_sn, exposure_time
    )

    if rgb_image is None or depth_image is None:
        print("❌ Failed to capture data from RVC camera. Please check camera connection, exposure settings, or device serial number.")
        sys.exit(1)

    print("✓ Successfully captured data from RVC camera")

    # Convert to PIL and CV formats (using RGB image)
    pil_image = PIL.Image.fromarray(rgb_image)
    cv_image = cv2.cvtColor(rgb_image, cv2.COLOR_RGB2BGR)

    # yolo infer
    print("=== Running YOLO detection ===")
    detections = detector.predict(cv_image)
    
    N = len(detections['bboxes'])

    if N == 0:
        print("No objects detected with the current settings.")
        sys.exit(0)

    # Filter detections by label 0
    filtered_indices = [i for i, label in enumerate(detections['labels']) if label == 0]
    
    if not filtered_indices:
        print("No objects with label 0 detected.")
        sys.exit(0)
    
    # Take the first detection with label 0
    first_label_0_index = filtered_indices[0]
    bbox = detections['bboxes'][first_label_0_index]
    print(f"Detected object bbox: {bbox}")

    # sam infer
    print("=== Running SAM segmentation ===")
    points, point_labels = bbox2points(bbox)
    sam_predictor.set_image(pil_image)
    mask, _, _ = sam_predictor.predict(points, point_labels)
    mask = (mask[0, 0] > 0).detach().cpu().numpy()
    mask = mask * 255

    # Visualization
    if True:
        # Enable interactive mode for real-time display
        plt.ion()
        
        # Create figure for visualization
        plt.figure(figsize=(12, 6))
        
        subplot_notick(2, N, 1)
        plt.imshow(pil_image)
        draw_bbox(bbox)
        plt.title("Detection")

        subplot_notick(2, N, 2)
        plt.imshow(pil_image)
        plt.imshow(mask, alpha=0.5)
        plt.title("Segmentation Mask")
        plt.subplots_adjust(wspace=0.05, hspace=0.2)
        
        # Save to file (existing functionality)
        plt.savefig('out.png', bbox_inches="tight")
        print("Visualization saved to 'out.png'")
        
        # Display in real-time window
        plt.show()
        plt.pause(0.001)  # Small pause to ensure the plot is displayed
        
        # Keep the window open until user closes it or presses a key
        input("Press Enter to continue...")
        
        # Close the figure to free resources
        plt.close()

    # Use captured depth image (already in correct format)
    depth = depth_image  # depth unit: m (already processed from RVC camera)

    # Use converted RGB image for point cloud
    rgb = rgb_image

    # Use camera intrinsic from RVC camera (already obtained)
    print("Using camera intrinsic matrix from RVC camera:")
    print(camera_intrinsic)

    print("=== Generating point cloud from mask ===")
    pcd_scene = mask_to_point_cloud(rgb, depth, mask, camera_intrinsic)

    if pcd_scene is None or len(np.asarray(pcd_scene.points)) == 0:
        print("Failed to generate point cloud from mask. Exiting...")
        sys.exit(1)

    print(f"Generated point cloud with {len(np.asarray(pcd_scene.points))} points")

    # estimation
    print("=== Loading template and running pose estimation ===")
    source_pcd = o3d.io.read_point_cloud(template)
    target_pcd = pcd_scene

    print(f"Template point cloud has {len(np.asarray(source_pcd.points))} points")
    print(f"Scene point cloud has {len(np.asarray(target_pcd.points))} points")

    T_cam_to_world = np.array([
        [1, 0, 0, 0],
        [0, 1, 0, 0],
        [0, 0, 1, 0],
        [0, 0, 0, 1]
    ])
    source_pcd_world = copy.deepcopy(source_pcd)
    source_pcd_world.transform(T_cam_to_world)
    target_pcd_world = copy.deepcopy(target_pcd)
    target_pcd_world.transform(T_cam_to_world)

    # Initialize pose estimator
    pose_estimator = PoseEstimator(source_pcd_world, target_pcd_world, voxel_size=0.002)
    T_source_to_target = pose_estimator.run()
    pose_estimator.visualize_registration(save_dir="out/T1", visualize_merged_clouds=False)
    
    # gripper pose in robot base frame
    gripper_pose_in_source = np.array([207.96, 269.18, 195.29, 128.88, -1, 87.52]) # x, y, z, rx, ry, rz
    gripper_pose_in_source_matrix = pose_6d_to_matrix(gripper_pose_in_source, unit='mm')

    # gripper pose in target frame
    gripper_pose_in_target_matrix = T_source_to_target @ gripper_pose_in_source_matrix 
    gripper_pose_in_target_euler = matrix_to_pose_6d(gripper_pose_in_target_matrix, unit='m')
    print(f"gripper_pose_in_target_euler: {gripper_pose_in_target_euler}")

    gripper_pose_in_target_axis = matrix_to_axis_6d(gripper_pose_in_target_matrix, unit='m')
    print(f"gripper_pose_in_target_axis: {gripper_pose_in_target_axis}")


    get_user_confirmation()
    # move robot
    with URRobotController("*************") as robot:
        robot.activate_gripper()
        robot.open_gripper()

        gripper_pose_in_target_axis[0] -= 0.2
        robot.move_linear(gripper_pose_in_target_axis, 0.05, 0.05)
        gripper_pose_in_target_axis[0] += 0.2
        robot.move_linear(gripper_pose_in_target_axis, 0.05, 0.05)
        robot.close_gripper()

        gripper_pose_in_target_axis[2] += 0.4
        robot.move_linear(gripper_pose_in_target_axis, 0.05, 0.05)

        position_take_pic_euler = np.array([769.96, -253.86, 363.07, 180, -0.0, -90.0]) # x, y, z, rx, ry, rz
        position_take_pic_matrix = pose_6d_to_matrix(position_take_pic_euler, unit='mm')
        position_take_pic_axis = matrix_to_axis_6d(position_take_pic_matrix, unit='m')
        position_take_pic_axis[0] -= 0.2
        robot.move_linear(position_take_pic_axis, 0.05, 0.05)
        position_take_pic_axis[0] += 0.2
        robot.move_linear(position_take_pic_axis, 0.05, 0.05)

        robot.disconnect()

if __name__ == "__main__":
    main()