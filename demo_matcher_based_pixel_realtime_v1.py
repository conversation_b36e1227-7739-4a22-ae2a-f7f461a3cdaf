import numpy as np
import cv2
import torch
import sys
from pathlib import Path
from camera.HKcam.hk_camera_manager import HKCameraManager

from robo_ctl.UR.ur_robot_controller import URRobotController
from utils import pose_6d_to_matrix, matrix_to_axis_6d
from template_matcher.utils_rot import calculate_angle_with_horizontal
from template_matcher.utils_matcher import detect_circle_in_roi

# Add preprocess directory to Python path
sys.path.append(str(Path(__file__).resolve().parent / 'preprocess'))
from preprocess.infer_wrap import YOLOv8TRTDetector


def get_user_confirmation():
    """Get user confirmation to move the robot."""
    user_input = input("Move the robot? (y/n): ").lower()
    if user_input == 'n':
        print("Operation cancelled.")
        exit()
    elif user_input != 'y':
        print("Invalid input!")
        exit()
    print("Robot moving!")


def capture_and_detect_circles(camera, detector):
    """Capture image and detect circles in ROIs."""
    # Capture image
    scene_bgr = camera.capture_single_image(None)
    if scene_bgr is None:
        print("Failed to capture image from HK camera.")
        return None, None
    
    # Convert to grayscale and enhance contrast
    scene_gray = cv2.cvtColor(scene_bgr, cv2.COLOR_BGR2GRAY)
    blur = cv2.GaussianBlur(scene_gray, (9,9), 2)
    clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
    enh = clahe.apply(blur)
        
    # Perform YOLO detection
    detections = detector.predict(scene_bgr)
    
    # Initialize variables for ROI centers
    large_center = None
    small_center = None
    
    # Process detections
    for bbox, label in zip(detections['bboxes'], detections['labels']):
        # Convert bounding box coordinates to integers
        x1, y1, x2, y2 = map(int, bbox)
        # Draw ROI bounding box
        color = (0, 255, 0) if label == 1 else (255, 0, 0)  # Green for large (1), Blue for small (2)
        cv2.rectangle(scene_bgr, (x1, y1), (x2, y2), color, 2)
        
        # Extract ROI from scene image
        roi_gray = enh[y1:y2, x1:x2]
        
        # Map labels to ROI types
        # label = 1 corresponds to large_roi
        # label = 2 corresponds to small_roi
        if label == 1:
            # Detect circles in large ROI
            large_circles = detect_circle_in_roi(roi_gray, x1, y1, "large")
            if large_circles:
                # Use the first detected circle
                large_center = (large_circles[0][0], large_circles[0][1])
                print(f"Large circle center: {large_center}, radius: {large_circles[0][2]}")
                # Draw circle and center
                cv2.circle(scene_bgr, large_center, large_circles[0][2], (0, 0, 255), 2)  # Red circle boundary
                cv2.circle(scene_bgr, large_center, 1, (0, 255, 255), 3)  # Yellow center point
        elif label == 2:
            # Detect circles in small ROI
            small_circles = detect_circle_in_roi(roi_gray, x1, y1, "small")
            if small_circles:
                # Use the first detected circle
                small_center = (small_circles[0][0], small_circles[0][1])
                print(f"Small circle center: {small_center}, radius: {small_circles[0][2]}")
                # Draw circle and center
                cv2.circle(scene_bgr, small_center, small_circles[0][2], (0, 0, 255), 2)  # Red circle boundary
                cv2.circle(scene_bgr, small_center, 1, (0, 255, 255), 3)  # Yellow center point
    
    # Check if both centers were detected
    if large_center is None or small_center is None:
        print("Could not detect both large and small circles.")
        return None, None
    
    # Display visualized image
    cv2.namedWindow('Detection Visualization', cv2.WINDOW_NORMAL)
    cv2.imshow('Detection Visualization', scene_bgr)
    cv2.waitKey(0)
    cv2.destroyAllWindows()
    
    return large_center, small_center


def calculate_world_delta(large_center, small_center):
    """Calculate world delta based on circle positions."""
    # Transformation matrix (camera to world)
    R_cam_to_world = np.array([
        [-3.05701394e-06, 6.36506259e-05, -3.98354809e-01],
        [-6.31956155e-05, -4.21291944e-06, 2.21344384e-01],
        [0, 0, 1]
    ])

    # Unused matrix - kept for compatibility
    R_world_to_cam = np.array([
        [ -1044.01138588, -15773.37975983,   3075.46206763],
        [ 15660.62279347,   -757.56429944,   6406.1670116 ],
        [0, 0, 1]
    ])

    large_world = R_cam_to_world @ np.array([large_center[0], large_center[1], 1])
    small_world = R_cam_to_world @ np.array([small_center[0], small_center[1], 1])
    world_delta = small_world - large_world
    
    print(f"Large world coordinates: {large_world}")
    print(f"World delta: dx=0, dy={world_delta[0]:.4f}, dz={world_delta[1]:.4f}")
    
    return world_delta, large_world


def calculate_rotation_angle(large_world):
    """Calculate rotation angle based on large circle position."""
    tcp_xyz = np.array([0.76383, -0.24167, 0.36874])
    large_center_xyz = np.array([tcp_xyz[0], large_world[0], large_world[1]])
    initial_angle = calculate_angle_with_horizontal(tcp_xyz, large_center_xyz)
    rot_angle = - (90 + 28.44) - initial_angle
    print(f"Large center XYZ: {large_center_xyz}")
    print(f"Initial angle: {initial_angle:.4f} degrees")
    print("-" * 30)
    print(f"Rotation angle: {rot_angle:.2f} degrees")
    
    return rot_angle


def move_robot_complete(rot_angle=None, world_delta=None):
    """Move the robot through both stages in a single execution."""
    with URRobotController("192.168.25.15") as robot:
        # Stage 1: Rotation movement
        if rot_angle is not None:
            print("Executing Stage 1: Rotation movement")
            # Initial position
            take_pic_euler = np.array([0.76383, -0.24167, 0.36874, 180, -0.0, -90.0])
            take_pic_matrix = pose_6d_to_matrix(take_pic_euler, unit='m')
            take_pic_axis = matrix_to_axis_6d(take_pic_matrix, unit='m')
            
            # Translation to align centers
            take_pic_axis[0] -= 0.15
            robot.move_linear(take_pic_axis, 0.05, 0.05)
            
            take_pic_axis[0] += 0.15
            take_pic_axis[1] = -0.1
            take_pic_axis[2] = 0.332
            Rx, Ry, Rz = take_pic_euler[3], take_pic_euler[4] + rot_angle, take_pic_euler[5]
            target_euler = np.array([take_pic_axis[0], take_pic_axis[1], take_pic_axis[2], Rx, Ry, Rz])
            target_matrix = pose_6d_to_matrix(target_euler, unit='m')
            target_axis = matrix_to_axis_6d(target_matrix, unit='m')
            robot.move_linear(target_axis, 0.05, 0.05)
            print("Stage 1: Rotation movement completed")
        
        # Stage 2: Position adjustment movement
        if world_delta is not None:
            print("Executing Stage 2: Position adjustment movement")
            position = robot.get_tcp_pose()
            position[0] -= 0.15
            robot.move_linear(position, 0.05, 0.05)
            
            position[0] += 0.12
            position[1] += world_delta[0]
            position[2] += world_delta[1]
            robot.move_linear(position, 0.05, 0.05)
            print("Stage 2: Position adjustment movement completed")


def main():
    # Initialize camera and detector
    with HKCameraManager(index=0) as camera:
        # YOLO detector configuration
        device = 'cuda' if torch.cuda.is_available() else 'cpu'
        yolo_engine = 'preprocess/weights/yolov8_seatbelt_v1.engine'
        
        # Initialize YOLO detector
        detector = YOLOv8TRTDetector(yolo_engine, device)
        
        print("Starting Stage 1: Rotation calibration")
        # Stage 1: Capture image and calculate rotation angle
        large_center, small_center = capture_and_detect_circles(camera, detector)
        if large_center is None or small_center is None:
            return
        
        # Calculate world coordinates and delta
        world_delta, large_world = calculate_world_delta(large_center, small_center)
        
        # Calculate rotation angle
        rot_angle = calculate_rotation_angle(large_world)
        
        # Get user confirmation before moving robot
        get_user_confirmation()
        
        # Execute Stage 1: Move robot to rotated position
        move_robot_complete(rot_angle=rot_angle)
        
        print("Capturing new image after rotation")
        # Capture new image after rotation
        large_center_after, small_center_after = capture_and_detect_circles(camera, detector)
        if large_center_after is None or small_center_after is None:
            return
        
        print("Starting Stage 2: Position calibration")
        # Stage 2: Calculate new world delta and move robot to final position
        world_delta_after, _ = calculate_world_delta(large_center_after, small_center_after)
        
        # Execute Stage 2: Move robot to final position
        get_user_confirmation()
        move_robot_complete(world_delta=world_delta_after)
        
        print("All stages completed successfully!")


if __name__ == "__main__":
    main()
